/**
 ******************************************************************************
 * @file    log_module.c
 * @brief   日志处理模块源文件
 * <AUTHOR>
 * @date    2025-08-04
 * @version 1.0.0
 ******************************************************************************
 * @attention
 *
 * 本模块实现完整的日志处理功能，包括：
 * - 基于FreeRTOS的异步日志处理
 * - 多级日志输出和过滤
 * - 线程安全的日志操作
 * - 时间戳和格式化输出
 * - 统计信息收集
 *
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "log_module.h"
#include "usart.h"
#include <stdlib.h>
#include <string.h>

/* Private defines -----------------------------------------------------------*/
#define LOG_MUTEX_TIMEOUT 1000       /**< 互斥锁超时时间(ms) */
#define LOG_QUEUE_TIMEOUT 100        /**< 队列超时时间(ms) */
#define LOG_TIMESTAMP_BUFFER_SIZE 32 /**< 时间戳缓冲区大小 */

/* Private variables ---------------------------------------------------------*/
static const char *log_level_strings[] = {
    "DEBUG",
    "INFO ",
    "WARN ",
    "ERROR"};

static const char *log_level_colors[] = {
    LOG_COLOR_BLUE,   // DEBUG
    LOG_COLOR_GREEN,  // INFO
    LOG_COLOR_YELLOW, // WARN
    LOG_COLOR_RED     // ERROR
};

/* Private function prototypes -----------------------------------------------*/
static void logTask(void *argument);
static void formatLogMessage(const log_handle_t *handle, const log_message_t *msg,
                             char *output, size_t output_size);
static void getTimestamp(char *buffer, size_t buffer_size);
static const char *getFileName(const char *path);

/* Public functions ----------------------------------------------------------*/

/**
 * @brief 初始化日志模块
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 日志模块句柄
 * @param config 日志配置参数（可为NULL使用默认配置）
 * @return 错误码
 * @note 必须在使用其他功能前调用此函数
 */
log_error_t log_init(log_handle_t *handle, const log_config_t *config)
{
    if (!handle)
    {
        return LOG_ERROR_PARAM;
    }

    // 检查是否已初始化
    if (handle->initialized)
    {
        return LOG_ERROR;
    }

    // 清零句柄
    memset(handle, 0, sizeof(log_handle_t));

    // 设置配置参数
    if (config)
    {
        handle->config = *config;
    }
    else
    {
        // 使用默认配置
        handle->config.min_level = LOG_LEVEL_DEBUG;
        handle->config.enable_timestamp = true;
        handle->config.enable_level_tag = true;
        handle->config.enable_file_line = true;
        handle->config.enable_function_name = true;
        handle->config.enable_color = false;
        handle->config.buffer_size = LOG_BUFFER_SIZE;
        handle->config.queue_size = LOG_QUEUE_SIZE;
    }

    // 分配缓冲区
    handle->log_buffer = (char *)malloc(handle->config.buffer_size);
    if (!handle->log_buffer)
    {
        return LOG_ERROR_NO_MEMORY;
    }

    // 创建FreeRTOS对象
    const osMessageQueueAttr_t queue_attr = {
        .name = "LogQueue"};

    handle->log_queue = osMessageQueueNew(handle->config.queue_size,
                                          sizeof(log_message_t), &queue_attr);
    if (!handle->log_queue)
    {
        free(handle->log_buffer);
        return LOG_ERROR_NO_MEMORY;
    }

    const osMutexAttr_t mutex_attr = {
        .name = "LogMutex"};

    handle->log_mutex = osMutexNew(&mutex_attr);
    if (!handle->log_mutex)
    {
        osMessageQueueDelete(handle->log_queue);
        free(handle->log_buffer);
        return LOG_ERROR_NO_MEMORY;
    }

    // 初始化状态
    handle->state = LOG_STATE_READY;
    handle->initialized = true;
    handle->enabled = true;

    return LOG_OK;
}

/**
 * @brief 反初始化日志模块
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 日志模块句柄
 * @return 错误码
 */
log_error_t log_deinit(log_handle_t *handle)
{
    if (!handle || !handle->initialized)
    {
        return LOG_ERROR_NOT_INIT;
    }

    // 停止模块
    log_stop(handle);

    // 删除FreeRTOS对象
    if (handle->log_queue)
    {
        osMessageQueueDelete(handle->log_queue);
        handle->log_queue = NULL;
    }

    if (handle->log_mutex)
    {
        osMutexDelete(handle->log_mutex);
        handle->log_mutex = NULL;
    }

    // 释放缓冲区
    if (handle->log_buffer)
    {
        free(handle->log_buffer);
        handle->log_buffer = NULL;
    }

    // 清零句柄
    memset(handle, 0, sizeof(log_handle_t));

    return LOG_OK;
}

/**
 * @brief 启动日志模块
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 日志模块句柄
 * @return 错误码
 * @note 启动日志处理任务
 */
log_error_t log_start(log_handle_t *handle)
{
    if (!handle || !handle->initialized)
    {
        return LOG_ERROR_NOT_INIT;
    }

    if (handle->state != LOG_STATE_READY)
    {
        return LOG_ERROR;
    }

    // 创建日志任务
    const osThreadAttr_t task_attr = {
        .name = "LogTask",
        .stack_size = LOG_TASK_STACK_SIZE,
        .priority = LOG_TASK_PRIORITY};

    handle->log_task_handle = osThreadNew(logTask, handle, &task_attr);
    if (!handle->log_task_handle)
    {
        return LOG_ERROR_NO_MEMORY;
    }

    handle->state = LOG_STATE_RUNNING;
    return LOG_OK;
}

/**
 * @brief 停止日志模块
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 日志模块句柄
 * @return 错误码
 */
log_error_t log_stop(log_handle_t *handle)
{
    if (!handle || !handle->initialized)
    {
        return LOG_ERROR_NOT_INIT;
    }

    // 终止任务
    if (handle->log_task_handle)
    {
        osThreadTerminate(handle->log_task_handle);
        handle->log_task_handle = NULL;
    }

    handle->state = LOG_STATE_READY;
    return LOG_OK;
}

/**
 * @brief 输出日志消息
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 日志模块句柄
 * @param level 日志级别
 * @param file 源文件名
 * @param function 函数名
 * @param line 行号
 * @param format 格式化字符串
 * @param ... 可变参数
 * @return 错误码
 */
log_error_t log_output(log_handle_t *handle, log_level_t level,
                       const char *file, const char *function, uint32_t line,
                       const char *format, ...)
{
    if (!handle || !handle->initialized || !handle->enabled)
    {
        return LOG_ERROR_NOT_INIT;
    }

    // 检查日志级别
    if (level < handle->config.min_level || level >= LOG_LEVEL_NONE)
    {
        return LOG_OK; // 不输出，但不是错误
    }

    // 创建日志消息
    log_message_t message;
    message.level = level;
    message.timestamp = osKernelGetTickCount();
    message.file = file;
    message.function = function;
    message.line = line;

    // 格式化消息内容
    va_list args;
    va_start(args, format);
    vsnprintf(message.message, sizeof(message.message), format, args);
    va_end(args);

    // 发送到队列
    osStatus_t status = osMessageQueuePut(handle->log_queue, &message, 0, LOG_QUEUE_TIMEOUT);
    if (status != osOK)
    {
        handle->stats.dropped_messages++;
        return LOG_ERROR_BUFFER_FULL;
    }

    // 更新统计信息
    handle->stats.total_messages++;
    switch (level)
    {
    case LOG_LEVEL_DEBUG:
        handle->stats.debug_count++;
        break;
    case LOG_LEVEL_INFO:
        handle->stats.info_count++;
        break;
    case LOG_LEVEL_WARN:
        handle->stats.warn_count++;
        break;
    case LOG_LEVEL_ERROR:
        handle->stats.error_count++;
        break;
    default:
        break;
    }

    return LOG_OK;
}

/**
 * @brief 设置日志级别
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 日志模块句柄
 * @param level 最小输出级别
 * @return 错误码
 */
log_error_t log_set_level(log_handle_t *handle, log_level_t level)
{
    if (!handle || !handle->initialized)
    {
        return LOG_ERROR_NOT_INIT;
    }

    if (level > LOG_LEVEL_NONE)
    {
        return LOG_ERROR_PARAM;
    }

    handle->config.min_level = level;
    return LOG_OK;
}

/**
 * @brief 获取日志级别
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 日志模块句柄
 * @return 当前日志级别
 */
log_level_t log_get_level(const log_handle_t *handle)
{
    if (!handle || !handle->initialized)
    {
        return LOG_LEVEL_NONE;
    }

    return handle->config.min_level;
}

/**
 * @brief 启用/禁用日志输出
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 日志模块句柄
 * @param enable true:启用 false:禁用
 * @return 错误码
 */
log_error_t log_enable(log_handle_t *handle, bool enable)
{
    if (!handle || !handle->initialized)
    {
        return LOG_ERROR_NOT_INIT;
    }

    handle->enabled = enable;
    return LOG_OK;
}

/**
 * @brief 获取日志统计信息
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 日志模块句柄
 * @param stats 统计信息指针
 * @return 错误码
 */
log_error_t log_get_statistics(const log_handle_t *handle, log_statistics_t *stats)
{
    if (!handle || !handle->initialized || !stats)
    {
        return LOG_ERROR_PARAM;
    }

    *stats = handle->stats;
    return LOG_OK;
}

/**
 * @brief 清除日志统计信息
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 日志模块句柄
 * @return 错误码
 */
log_error_t log_clear_statistics(log_handle_t *handle)
{
    if (!handle || !handle->initialized)
    {
        return LOG_ERROR_NOT_INIT;
    }

    memset(&handle->stats, 0, sizeof(log_statistics_t));
    return LOG_OK;
}

/**
 * @brief 获取模块版本信息
 * <AUTHOR>
 * @date 2025-08-04
 * @param major 主版本号指针
 * @param minor 次版本号指针
 * @param patch 补丁版本号指针
 */
void log_get_version(uint8_t *major, uint8_t *minor, uint8_t *patch)
{
    if (major)
        *major = LOG_MODULE_VERSION_MAJOR;
    if (minor)
        *minor = LOG_MODULE_VERSION_MINOR;
    if (patch)
        *patch = LOG_MODULE_VERSION_PATCH;
}

/* Private functions ---------------------------------------------------------*/

/**
 * @brief 日志处理任务
 * @param argument 任务参数(日志模块句柄)
 */
static void logTask(void *argument)
{
    log_handle_t *handle = (log_handle_t *)argument;
    log_message_t message;
    char formatted_message[LOG_MAX_MESSAGE_LENGTH * 2];

    while (1)
    {
        // 等待日志消息
        osStatus_t status = osMessageQueueGet(handle->log_queue, &message, NULL, osWaitForever);
        if (status == osOK)
        {
            // 获取互斥锁
            if (osMutexAcquire(handle->log_mutex, LOG_MUTEX_TIMEOUT) == osOK)
            {
                // 格式化日志消息
                formatLogMessage(handle, &message, formatted_message, sizeof(formatted_message));

                // 输出到USART2
                printf("%s", formatted_message);

                // 释放互斥锁
                osMutexRelease(handle->log_mutex);
            }
        }
    }
}

/**
 * @brief 格式化日志消息
 * @param handle 日志模块句柄
 * @param msg 日志消息
 * @param output 输出缓冲区
 * @param output_size 输出缓冲区大小
 */
static void formatLogMessage(const log_handle_t *handle, const log_message_t *msg,
                             char *output, size_t output_size)
{
    char timestamp_str[LOG_TIMESTAMP_BUFFER_SIZE];
    char *ptr = output;
    size_t remaining = output_size - 1; // 保留结束符位置

    // 清空输出缓冲区
    memset(output, 0, output_size);

    // 添加颜色（如果启用）
    if (handle->config.enable_color && msg->level < LOG_LEVEL_NONE)
    {
        int len = snprintf(ptr, remaining, "%s", log_level_colors[msg->level]);
        if (len > 0 && len < remaining)
        {
            ptr += len;
            remaining -= len;
        }
    }

    // 添加时间戳
    if (handle->config.enable_timestamp)
    {
        getTimestamp(timestamp_str, sizeof(timestamp_str));
        int len = snprintf(ptr, remaining, "[%s] ", timestamp_str);
        if (len > 0 && len < remaining)
        {
            ptr += len;
            remaining -= len;
        }
    }

    // 添加日志级别标签
    if (handle->config.enable_level_tag && msg->level < LOG_LEVEL_NONE)
    {
        int len = snprintf(ptr, remaining, "[%s] ", log_level_strings[msg->level]);
        if (len > 0 && len < remaining)
        {
            ptr += len;
            remaining -= len;
        }
    }

    // 添加文件名和行号
    if (handle->config.enable_file_line && msg->file && msg->line > 0)
    {
        const char *filename = getFileName(msg->file);
        int len = snprintf(ptr, remaining, "[%s:%lu] ", filename, msg->line);
        if (len > 0 && len < remaining)
        {
            ptr += len;
            remaining -= len;
        }
    }

    // 添加函数名
    if (handle->config.enable_function_name && msg->function)
    {
        int len = snprintf(ptr, remaining, "[%s] ", msg->function);
        if (len > 0 && len < remaining)
        {
            ptr += len;
            remaining -= len;
        }
    }

    // 添加消息内容
    int len = snprintf(ptr, remaining, "%s", msg->message);
    if (len > 0 && len < remaining)
    {
        ptr += len;
        remaining -= len;
    }

    // 添加颜色重置（如果启用）
    if (handle->config.enable_color)
    {
        len = snprintf(ptr, remaining, "%s", LOG_COLOR_RESET);
        if (len > 0 && len < remaining)
        {
            ptr += len;
            remaining -= len;
        }
    }

    // 添加换行符
    if (remaining > 1)
    {
        snprintf(ptr, remaining, "\r\n");
    }
}

/**
 * @brief 获取时间戳字符串
 * @param buffer 缓冲区
 * @param buffer_size 缓冲区大小
 */
static void getTimestamp(char *buffer, size_t buffer_size)
{
    uint32_t tick = osKernelGetTickCount();
    uint32_t seconds = tick / 1000;
    uint32_t milliseconds = tick % 1000;

    snprintf(buffer, buffer_size, "%lu.%03lu", seconds, milliseconds);
}

/**
 * @brief 从完整路径中提取文件名
 * @param path 完整路径
 * @return 文件名指针
 */
static const char *getFileName(const char *path)
{
    if (!path)
        return "unknown";

    const char *filename = strrchr(path, '/');
    if (!filename)
        filename = strrchr(path, '\\');

    return filename ? filename + 1 : path;
}

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
