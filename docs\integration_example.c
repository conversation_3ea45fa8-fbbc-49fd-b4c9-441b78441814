/**
 ******************************************************************************
 * @file    integration_example.c
 * @brief   串口通信模块集成示例
 * <AUTHOR>
 * @date    2025-08-04
 * @version 1.0.0
 ******************************************************************************
 * @attention
 * 
 * 本文件展示如何在STM32G4项目中集成和使用串口通信模块
 * 
 * 集成步骤：
 * 1. 包含必要的头文件
 * 2. 定义全局通信句柄
 * 3. 实现用户命令回调函数
 * 4. 在main函数中初始化和启动模块
 * 5. 实现UART接收完成回调
 * 
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "cmsis_os.h"
#include "gpio.h"
#include "led.h"
#include "usart.h"
#include "comm_module.h"  // 添加通信模块头文件

/* Private variables ---------------------------------------------------------*/
comm_handle_t g_comm_handle;  // 全局通信模块句柄

/* Private function prototypes -----------------------------------------------*/
comm_error_t user_command_callback(comm_cmd_type_t cmd_type, 
                                  const uint8_t *data, 
                                  uint8_t len,
                                  uint8_t *response_data, 
                                  uint8_t *response_len);

/**
 * @brief 用户命令回调函数
 * <AUTHOR>
 * @date 2025-08-04
 * @param cmd_type 命令类型
 * @param data 接收数据指针
 * @param len 数据长度
 * @param response_data 响应数据指针
 * @param response_len 响应数据长度指针
 * @return 错误码
 * @note 处理用户自定义命令
 */
comm_error_t user_command_callback(comm_cmd_type_t cmd_type, 
                                  const uint8_t *data, 
                                  uint8_t len,
                                  uint8_t *response_data, 
                                  uint8_t *response_len)
{
    switch (cmd_type)
    {
    case COMM_CMD_LED_CONTROL:
        // LED控制命令：数据格式 [LED_ID, STATE]
        if (len >= 2)
        {
            uint8_t led_id = data[0];    // LED编号 (1或2)
            uint8_t led_state = data[1]; // LED状态 (0=关闭, 1=开启)
            
            // 根据LED编号选择对应的引脚和端口
            uint16_t led_pin = (led_id == 1) ? LED1_PIN : LED2_PIN;
            GPIO_TypeDef* led_port = (led_id == 1) ? LED1_PORT : LED2_PORT;
            
            // 控制LED
            LED_Control(led_pin, led_port, led_state ? LED_ON : LED_OFF);
            
            // 构建响应数据：返回当前LED状态
            response_data[0] = led_id;
            response_data[1] = led_state;
            *response_len = 2;
            
            return COMM_OK;
        }
        return COMM_ERROR_PARAM;
        
    case COMM_CMD_GET_CONFIG:
        // 获取配置信息命令
        if (len == 1)
        {
            uint8_t config_id = data[0];
            switch (config_id)
            {
            case 0x01: // 获取系统时钟频率
                {
                    uint32_t sysclk = HAL_RCC_GetSysClockFreq();
                    response_data[0] = (sysclk >> 24) & 0xFF;
                    response_data[1] = (sysclk >> 16) & 0xFF;
                    response_data[2] = (sysclk >> 8) & 0xFF;
                    response_data[3] = sysclk & 0xFF;
                    *response_len = 4;
                    return COMM_OK;
                }
            case 0x02: // 获取UART配置
                {
                    response_data[0] = (g_comm_handle.config.baudrate >> 24) & 0xFF;
                    response_data[1] = (g_comm_handle.config.baudrate >> 16) & 0xFF;
                    response_data[2] = (g_comm_handle.config.baudrate >> 8) & 0xFF;
                    response_data[3] = g_comm_handle.config.baudrate & 0xFF;
                    response_data[4] = g_comm_handle.config.word_length;
                    response_data[5] = g_comm_handle.config.stop_bits;
                    response_data[6] = g_comm_handle.config.parity;
                    *response_len = 7;
                    return COMM_OK;
                }
            default:
                return COMM_ERROR_PARAM;
            }
        }
        return COMM_ERROR_PARAM;
        
    case COMM_CMD_SET_CONFIG:
        // 设置配置信息命令
        if (len >= 2)
        {
            uint8_t config_id = data[0];
            switch (config_id)
            {
            case 0x01: // 设置LED闪烁频率（示例）
                if (len == 2)
                {
                    uint8_t blink_freq = data[1];
                    // 这里可以设置LED闪烁频率
                    // 实际实现需要根据具体需求
                    response_data[0] = config_id;
                    response_data[1] = blink_freq;
                    *response_len = 2;
                    return COMM_OK;
                }
                break;
            default:
                return COMM_ERROR_PARAM;
            }
        }
        return COMM_ERROR_PARAM;
        
    default:
        // 不支持的命令
        return COMM_ERROR_UNSUPPORTED;
    }
}

/**
 * @brief  The application entry point.
 * @retval int
 */
int main(void)
{
    /* USER CODE BEGIN 1 */

    /* USER CODE END 1 */

    /* MCU Configuration--------------------------------------------------------*/

    /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
    HAL_Init();

    /* USER CODE BEGIN Init */

    /* USER CODE END Init */

    /* Configure the system clock */
    SystemClock_Config();

    /* USER CODE BEGIN SysInit */

    /* USER CODE END SysInit */

    /* Initialize all configured peripherals */
    MX_GPIO_Init();
    LED_Init();
    MX_USART1_UART_Init();
    
    /* USER CODE BEGIN 2 */
    
    // 初始化通信模块
    comm_error_t comm_result = comm_init(&g_comm_handle, &huart1, NULL);
    if (comm_result != COMM_OK)
    {
        // 初始化失败，进入错误处理
        Error_Handler();
    }
    
    // 注册用户命令回调函数
    comm_result = comm_register_callback(&g_comm_handle, user_command_callback);
    if (comm_result != COMM_OK)
    {
        Error_Handler();
    }

    /* USER CODE END 2 */

    /* Init scheduler */
    osKernelInitialize(); /* Call init function for freertos objects (in freertos.c) */
    MX_FREERTOS_Init();
    
    /* USER CODE BEGIN RTOS_MUTEX */
    /* add mutexes, ... */
    /* USER CODE END RTOS_MUTEX */

    /* USER CODE BEGIN RTOS_SEMAPHORES */
    /* add semaphores, ... */
    /* USER CODE END RTOS_SEMAPHORES */

    /* USER CODE BEGIN RTOS_TIMERS */
    /* start timers, add new ones, ... */
    /* USER CODE END RTOS_TIMERS */

    /* USER CODE BEGIN RTOS_QUEUES */
    /* add queues, ... */
    /* USER CODE END RTOS_QUEUES */

    /* Create the thread(s) */
    /* creation of defaultTask */
    
    /* USER CODE BEGIN RTOS_THREADS */
    
    // 启动通信模块（创建接收和发送任务）
    comm_result = comm_start(&g_comm_handle);
    if (comm_result != COMM_OK)
    {
        Error_Handler();
    }
    
    /* USER CODE END RTOS_THREADS */

    /* USER CODE BEGIN RTOS_EVENTS */
    /* add events, ... */
    /* USER CODE END RTOS_EVENTS */

    /* Start scheduler */
    osKernelStart();

    /* We should never get here as control is now taken by the scheduler */
    /* Infinite loop */
    /* USER CODE BEGIN WHILE */
    while (1)
    {
        /* USER CODE END WHILE */

        /* USER CODE BEGIN 3 */
    }
    /* USER CODE END 3 */
}

/**
 * @brief UART接收完成回调函数
 * <AUTHOR>
 * @date 2025-08-04
 * @param huart UART句柄
 * @note 需要在stm32g4xx_it.c中实现或在此处实现
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart == &huart1)
    {
        // 处理通信模块的接收完成
        g_comm_handle.rx_index++;
        
        // 检查缓冲区是否溢出
        if (g_comm_handle.rx_index >= COMM_RX_BUFFER_SIZE)
        {
            g_comm_handle.rx_index = 0;  // 重置索引
            g_comm_handle.stats.frame_errors++;  // 增加错误计数
        }
        
        // 继续接收下一个字节
        HAL_UART_Receive_IT(huart, &g_comm_handle.rx_buffer[g_comm_handle.rx_index], 1);
    }
}

/**
 * @brief 发送测试命令示例函数
 * <AUTHOR>
 * @date 2025-08-04
 * @note 这些函数可以在其他任务中调用来测试通信功能
 */

// 发送心跳命令
comm_error_t send_ping_command(void)
{
    return comm_send_data(&g_comm_handle, COMM_CMD_PING, NULL, 0, 1000);
}

// 获取版本信息
comm_error_t get_version_info(void)
{
    return comm_send_data(&g_comm_handle, COMM_CMD_GET_VERSION, NULL, 0, 1000);
}

// 控制LED
comm_error_t control_led_example(uint8_t led_id, uint8_t state)
{
    uint8_t data[2] = {led_id, state};
    return comm_send_data(&g_comm_handle, COMM_CMD_LED_CONTROL, data, 2, 1000);
}

// 获取系统时钟频率
comm_error_t get_system_clock(void)
{
    uint8_t config_id = 0x01;
    return comm_send_data(&g_comm_handle, COMM_CMD_GET_CONFIG, &config_id, 1, 1000);
}

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
