/**
 ******************************************************************************
 * @file    comm_module.h
 * @brief   串口通信模块头文件
 * <AUTHOR>
 * @date    2025-08-04
 * @version 1.0.0
 ******************************************************************************
 * @attention
 *
 * 本模块提供完整的串口通信功能，包括：
 * - 标准化的通信协议
 * - 命令解析和响应机制
 * - 错误处理和异常管理
 * - 可配置的串口参数
 *
 * 使用前请确保：
 * 1. 已正确初始化UART外设
 * 2. 已启用FreeRTOS
 * 3. 系统时钟配置正确
 *
 ******************************************************************************
 */

#ifndef __COMM_MODULE_H
#define __COMM_MODULE_H

#ifdef __cplusplus
extern "C"
{
#endif

/* Includes ------------------------------------------------------------------*/
#include "cmsis_os.h"
#include "stm32g4xx_hal.h"
#include <stdbool.h>
#include <stdint.h>

/* 模块版本信息 ---------------------------------------------------------------*/
#define COMM_MODULE_VERSION_MAJOR 1
#define COMM_MODULE_VERSION_MINOR 0
#define COMM_MODULE_VERSION_PATCH 0

/* 协议常量定义 ---------------------------------------------------------------*/
#define COMM_FRAME_HEADER 0xAA55  /**< 帧头标识 */
#define COMM_FRAME_TAIL 0x55AA    /**< 帧尾标识 */
#define COMM_MAX_PAYLOAD_SIZE 255 /**< 最大有效载荷长度 */
#define COMM_MIN_FRAME_SIZE 7     /**< 最小帧长度(不含有效载荷) */
#define COMM_MAX_FRAME_SIZE (COMM_MIN_FRAME_SIZE + COMM_MAX_PAYLOAD_SIZE)

/* 帧结构偏移量定义 -----------------------------------------------------------*/
#define COMM_OFFSET_HEADER 0   /**< 帧头偏移 */
#define COMM_OFFSET_CMD_TYPE 2 /**< 命令类型偏移 */
#define COMM_OFFSET_DATA_LEN 3 /**< 数据长度偏移 */
#define COMM_OFFSET_PAYLOAD 4  /**< 有效载荷偏移 */

/* 缓冲区大小定义 -------------------------------------------------------------*/
#define COMM_RX_BUFFER_SIZE 512 /**< 接收缓冲区大小 */
#define COMM_TX_BUFFER_SIZE 512 /**< 发送缓冲区大小 */
#define COMM_QUEUE_SIZE 10      /**< 消息队列大小 */

/* 超时时间定义 ---------------------------------------------------------------*/
#define COMM_DEFAULT_TIMEOUT 1000 /**< 默认超时时间(ms) */
#define COMM_ACK_TIMEOUT 500      /**< 应答超时时间(ms) */
#define COMM_RETRY_COUNT 3        /**< 重试次数 */

    /**
     * @brief 命令类型枚举
     * @note 可根据实际需求扩展命令类型
     */
    typedef enum
    {
        COMM_CMD_PING = 0x01,        /**< 心跳命令 */
        COMM_CMD_ACK = 0x02,         /**< 应答命令 */
        COMM_CMD_NACK = 0x03,        /**< 否定应答命令 */
        COMM_CMD_GET_VERSION = 0x10, /**< 获取版本信息 */
        COMM_CMD_GET_STATUS = 0x11,  /**< 获取状态信息 */
        COMM_CMD_SET_CONFIG = 0x20,  /**< 设置配置参数 */
        COMM_CMD_GET_CONFIG = 0x21,  /**< 获取配置参数 */
        COMM_CMD_RESET = 0x30,       /**< 复位命令 */
        COMM_CMD_LED_CONTROL = 0x40, /**< LED控制命令 */
        COMM_CMD_USER_DEFINED = 0x80 /**< 用户自定义命令起始 */
    } comm_cmd_type_t;

    /**
     * @brief 错误码枚举
     */
    typedef enum
    {
        COMM_OK = 0x00,                /**< 成功 */
        COMM_ERROR = 0x01,             /**< 一般错误 */
        COMM_ERROR_PARAM = 0x02,       /**< 参数错误 */
        COMM_ERROR_TIMEOUT = 0x03,     /**< 超时错误 */
        COMM_ERROR_CHECKSUM = 0x04,    /**< 校验错误 */
        COMM_ERROR_FRAME = 0x05,       /**< 帧格式错误 */
        COMM_ERROR_BUFFER_FULL = 0x06, /**< 缓冲区满 */
        COMM_ERROR_NOT_INIT = 0x07,    /**< 未初始化 */
        COMM_ERROR_BUSY = 0x08,        /**< 忙碌状态 */
        COMM_ERROR_NO_MEMORY = 0x09,   /**< 内存不足 */
        COMM_ERROR_UNSUPPORTED = 0x0A  /**< 不支持的操作 */
    } comm_error_t;

    /**
     * @brief 模块状态枚举
     */
    typedef enum
    {
        COMM_STATE_UNINITIALIZED = 0, /**< 未初始化 */
        COMM_STATE_READY,             /**< 就绪状态 */
        COMM_STATE_BUSY,              /**< 忙碌状态 */
        COMM_STATE_ERROR              /**< 错误状态 */
    } comm_state_t;

    /**
     * @brief 串口配置结构体
     */
    typedef struct
    {
        uint32_t baudrate;     /**< 波特率 */
        uint32_t word_length;  /**< 数据位长度 */
        uint32_t stop_bits;    /**< 停止位 */
        uint32_t parity;       /**< 奇偶校验 */
        uint32_t flow_control; /**< 流控制 */
        uint32_t timeout;      /**< 超时时间 */
    } comm_uart_config_t;

    /**
     * @brief 通信统计信息结构体
     */
    typedef struct
    {
        uint32_t tx_frames;       /**< 发送帧数 */
        uint32_t rx_frames;       /**< 接收帧数 */
        uint32_t tx_bytes;        /**< 发送字节数 */
        uint32_t rx_bytes;        /**< 接收字节数 */
        uint32_t frame_errors;    /**< 帧错误数 */
        uint32_t checksum_errors; /**< 校验错误数 */
        uint32_t timeout_errors;  /**< 超时错误数 */
        uint32_t retries;         /**< 重试次数 */
    } comm_statistics_t;

    /**
     * @brief 消息结构体
     */
    typedef struct
    {
        comm_cmd_type_t cmd_type;            /**< 命令类型 */
        uint8_t data_len;                    /**< 数据长度 */
        uint8_t data[COMM_MAX_PAYLOAD_SIZE]; /**< 数据内容 */
        uint32_t timestamp;                  /**< 时间戳 */
    } comm_message_t;

    /**
     * @brief 命令回调函数类型定义
     * @param cmd_type 命令类型
     * @param data 数据指针
     * @param len 数据长度
     * @param response_data 响应数据指针
     * @param response_len 响应数据长度指针
     * @return 错误码
     */
    typedef comm_error_t (*comm_cmd_callback_t)(comm_cmd_type_t cmd_type,
                                                const uint8_t *data,
                                                uint8_t len,
                                                uint8_t *response_data,
                                                uint8_t *response_len);

    /**
     * @brief 通信模块句柄结构体
     */
    typedef struct
    {
        UART_HandleTypeDef *huart; /**< UART句柄 */
        comm_uart_config_t config; /**< 串口配置 */
        comm_state_t state;        /**< 模块状态 */
        comm_statistics_t stats;   /**< 统计信息 */

        /* FreeRTOS对象 */
        osThreadId_t rx_task_handle; /**< 接收任务句柄 */
        osThreadId_t tx_task_handle; /**< 发送任务句柄 */
        osMessageQueueId_t rx_queue; /**< 接收队列 */
        osMessageQueueId_t tx_queue; /**< 发送队列 */
        osMutexId_t mutex;           /**< 互斥锁 */
        osTimerId_t timeout_timer;   /**< 超时定时器 */

        /* 缓冲区 */
        uint8_t rx_buffer[COMM_RX_BUFFER_SIZE]; /**< 接收缓冲区 */
        uint8_t tx_buffer[COMM_TX_BUFFER_SIZE]; /**< 发送缓冲区 */
        uint16_t rx_index;                      /**< 接收索引 */
        uint16_t tx_index;                      /**< 发送索引 */

        /* 回调函数 */
        comm_cmd_callback_t cmd_callback; /**< 命令回调函数 */

        /* 状态标志 */
        bool initialized;  /**< 初始化标志 */
        bool receiving;    /**< 接收状态标志 */
        bool transmitting; /**< 发送状态标志 */
    } comm_handle_t;

    /* 全局函数声明 ---------------------------------------------------------------*/

    /**
     * @brief 初始化通信模块
     * <AUTHOR>
     * @date 2025-08-04
     * @param handle 通信模块句柄
     * @param huart UART句柄
     * @param config 串口配置参数
     * @return 错误码
     * @note 必须在使用其他功能前调用此函数
     */
    comm_error_t comm_init(comm_handle_t *handle,
                           UART_HandleTypeDef *huart,
                           const comm_uart_config_t *config);

    /**
     * @brief 反初始化通信模块
     * <AUTHOR>
     * @date 2025-08-04
     * @param handle 通信模块句柄
     * @return 错误码
     */
    comm_error_t comm_deinit(comm_handle_t *handle);

    /**
     * @brief 启动通信模块
     * <AUTHOR>
     * @date 2025-08-04
     * @param handle 通信模块句柄
     * @return 错误码
     * @note 启动接收和发送任务
     */
    comm_error_t comm_start(comm_handle_t *handle);

    /**
     * @brief 停止通信模块
     * <AUTHOR>
     * @date 2025-08-04
     * @param handle 通信模块句柄
     * @return 错误码
     */
    comm_error_t comm_stop(comm_handle_t *handle);

    /**
     * @brief 发送数据
     * <AUTHOR>
     * @date 2025-08-04
     * @param handle 通信模块句柄
     * @param cmd_type 命令类型
     * @param data 数据指针
     * @param len 数据长度
     * @param timeout 超时时间(ms)
     * @return 错误码
     */
    comm_error_t comm_send_data(comm_handle_t *handle,
                                comm_cmd_type_t cmd_type,
                                const uint8_t *data,
                                uint8_t len,
                                uint32_t timeout);

    /**
     * @brief 发送应答
     * <AUTHOR>
     * @date 2025-08-04
     * @param handle 通信模块句柄
     * @param cmd_type 原命令类型
     * @return 错误码
     */
    comm_error_t comm_send_ack(comm_handle_t *handle, comm_cmd_type_t cmd_type);

    /**
     * @brief 发送否定应答
     * <AUTHOR>
     * @date 2025-08-04
     * @param handle 通信模块句柄
     * @param cmd_type 原命令类型
     * @param error_code 错误码
     * @return 错误码
     */
    comm_error_t comm_send_nack(comm_handle_t *handle,
                                comm_cmd_type_t cmd_type,
                                comm_error_t error_code);

    /**
     * @brief 注册命令回调函数
     * <AUTHOR>
     * @date 2025-08-04
     * @param handle 通信模块句柄
     * @param callback 回调函数指针
     * @return 错误码
     */
    comm_error_t comm_register_callback(comm_handle_t *handle,
                                        comm_cmd_callback_t callback);

    /**
     * @brief 获取模块状态
     * <AUTHOR>
     * @date 2025-08-04
     * @param handle 通信模块句柄
     * @return 模块状态
     */
    comm_state_t comm_get_state(const comm_handle_t *handle);

    /**
     * @brief 获取统计信息
     * <AUTHOR>
     * @date 2025-08-04
     * @param handle 通信模块句柄
     * @param stats 统计信息指针
     * @return 错误码
     */
    comm_error_t comm_get_statistics(const comm_handle_t *handle,
                                     comm_statistics_t *stats);

    /**
     * @brief 清除统计信息
     * <AUTHOR>
     * @date 2025-08-04
     * @param handle 通信模块句柄
     * @return 错误码
     */
    comm_error_t comm_clear_statistics(comm_handle_t *handle);

    /**
     * @brief 获取错误描述字符串
     * <AUTHOR>
     * @date 2025-08-04
     * @param error 错误码
     * @return 错误描述字符串
     */
    const char *comm_get_error_string(comm_error_t error);

    /**
     * @brief 获取模块版本信息
     * <AUTHOR>
     * @date 2025-08-04
     * @param major 主版本号指针
     * @param minor 次版本号指针
     * @param patch 补丁版本号指针
     */
    void comm_get_version(uint8_t *major, uint8_t *minor, uint8_t *patch);

    /* 内部函数声明(仅供模块内部使用) ----------------------------------------------*/

    /**
     * @brief 计算校验码
     * @param data 数据指针
     * @param len 数据长度
     * @return 校验码
     */
    uint8_t comm_calculate_checksum(const uint8_t *data, uint16_t len);

    /**
     * @brief 验证帧格式
     * @param frame 帧数据指针
     * @param len 帧长度
     * @return true:有效帧 false:无效帧
     */
    bool comm_validate_frame(const uint8_t *frame, uint16_t len);

#ifdef __cplusplus
}
#endif

#endif /* __COMM_MODULE_H */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
