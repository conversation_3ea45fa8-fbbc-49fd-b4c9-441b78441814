#ifndef __LED_H
#define __LED_H

#include "stm32g4xx_hal.h"

// LED引脚定义
#define LED1_PIN     GPIO_PIN_5
#define LED1_PORT    GPIOA
#define LED2_PIN     GPIO_PIN_6
#define LED2_PORT    GPIOA

// LED状态定义
typedef enum {
    LED_OFF = 0,
    LED_ON = 1
} LED_StateTypeDef;

// LED初始化函数
void LED_Init(void);

// LED控制函数
void LED_Control(uint16_t led_pin, GPIO_TypeDef* led_port, LED_StateTypeDef state);

// LED闪烁函数
void LED_Blink(uint16_t led_pin, GPIO_TypeDef* led_port, uint32_t delay_ms);

// LED任务函数
void led_task(void *argument);

#endif /* __LED_H */

