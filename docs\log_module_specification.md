# STM32G4 日志模块技术文档

**版本**: 1.0.0  
**作者**: mkx  
**日期**: 2025-08-04  
**适用平台**: STM32G4系列微控制器

## 1. 概述

本文档描述了STM32G4项目中日志处理模块的完整技术规范。该模块提供了多级日志输出、printf重定向、线程安全和性能优化等功能，专为嵌入式实时系统设计。

### 1.1 设计目标

- **多级日志**: 支持DEBUG、INFO、WARN、ERROR四个级别
- **线程安全**: 基于FreeRTOS的互斥锁保护
- **高性能**: 异步处理，不阻塞主程序
- **易用性**: 简洁的宏定义，类似printf的使用方式
- **可配置**: 支持运行时配置和级别过滤

### 1.2 主要特性

- ✅ **printf重定向**: 标准C库函数重定向到USART2
- ✅ **多级日志**: DEBUG/INFO/WARN/ERROR四个级别
- ✅ **时间戳**: 自动添加系统时间戳
- ✅ **文件信息**: 可选的文件名、函数名、行号信息
- ✅ **线程安全**: FreeRTOS互斥锁保护
- ✅ **异步处理**: 基于消息队列的异步输出
- ✅ **统计功能**: 实时统计各级别日志数量
- ✅ **级别过滤**: 运行时动态调整输出级别

## 2. 模块架构

### 2.1 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   应用代码      │    │   日志模块      │    │   USART2       │
│                 │    │                 │    │                 │
│ LOG_INFO(...)   │───▶│ 消息队列        │───▶│ printf重定向    │
│ LOG_ERROR(...)  │    │ 格式化处理      │    │ 串口输出        │
│ printf(...)     │    │ 线程安全        │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 核心组件

| 组件 | 功能 | 实现方式 |
|------|------|----------|
| 消息队列 | 异步日志处理 | FreeRTOS MessageQueue |
| 互斥锁 | 线程安全保护 | FreeRTOS Mutex |
| 日志任务 | 后台处理任务 | FreeRTOS Task |
| 格式化器 | 消息格式化 | 自定义格式化函数 |
| 统计模块 | 性能统计 | 内置计数器 |

## 3. API接口说明

### 3.1 初始化函数

```c
log_error_t log_init(log_handle_t *handle, const log_config_t *config);
```

**功能**: 初始化日志模块  
**参数**:
- `handle`: 日志模块句柄
- `config`: 配置参数（可为NULL使用默认配置）

**返回值**: 错误码

**默认配置**:
```c
log_config_t default_config = {
    .min_level = LOG_LEVEL_DEBUG,
    .enable_timestamp = true,
    .enable_level_tag = true,
    .enable_file_line = true,
    .enable_function_name = true,
    .enable_color = false,
    .buffer_size = 1024,
    .queue_size = 20
};
```

### 3.2 启动函数

```c
log_error_t log_start(log_handle_t *handle);
```

**功能**: 启动日志模块，创建后台处理任务  
**参数**: `handle` - 日志模块句柄  
**返回值**: 错误码

### 3.3 日志输出函数

```c
log_error_t log_output(log_handle_t *handle, log_level_t level, 
                      const char *file, const char *function, uint32_t line,
                      const char *format, ...);
```

**功能**: 输出格式化日志消息  
**参数**:
- `handle`: 日志模块句柄
- `level`: 日志级别
- `file`: 源文件名
- `function`: 函数名
- `line`: 行号
- `format`: 格式化字符串
- `...`: 可变参数

**返回值**: 错误码

### 3.4 配置函数

```c
// 设置日志级别
log_error_t log_set_level(log_handle_t *handle, log_level_t level);

// 获取日志级别
log_level_t log_get_level(const log_handle_t *handle);

// 启用/禁用日志
log_error_t log_enable(log_handle_t *handle, bool enable);
```

### 3.5 统计函数

```c
// 获取统计信息
log_error_t log_get_statistics(const log_handle_t *handle, log_statistics_t *stats);

// 清除统计信息
log_error_t log_clear_statistics(log_handle_t *handle);
```

## 4. 便捷宏定义

### 4.1 标准日志宏

```c
LOG_DEBUG(format, ...)   // 调试级别，包含文件信息
LOG_INFO(format, ...)    // 信息级别，包含文件信息
LOG_WARN(format, ...)    // 警告级别，包含文件信息
LOG_ERROR(format, ...)   // 错误级别，包含文件信息
```

### 4.2 简化日志宏

```c
LOGD(format, ...)        // 调试级别，不含文件信息
LOGI(format, ...)        // 信息级别，不含文件信息
LOGW(format, ...)        // 警告级别，不含文件信息
LOGE(format, ...)        // 错误级别，不含文件信息
```

### 4.3 条件日志宏

```c
LOG_DEBUG_IF(condition, format, ...)
LOG_INFO_IF(condition, format, ...)
LOG_WARN_IF(condition, format, ...)
LOG_ERROR_IF(condition, format, ...)
```

## 5. 日志格式

### 5.1 完整格式

```
[时间戳] [级别] [文件名:行号] [函数名] 消息内容
```

**示例**:
```
[12.345] [INFO ] [main.c:123] [main] System initialized successfully
[12.567] [ERROR] [sensor.c:45] [read_sensor] Sensor communication failed
```

### 5.2 简化格式

```
[时间戳] [级别] 消息内容
```

**示例**:
```
[12.345] [INFO ] Application started
[12.567] [WARN ] Low battery warning
```

### 5.3 格式配置

可通过配置结构体控制输出格式：

```c
log_config_t config = {
    .enable_timestamp = true,      // 启用时间戳
    .enable_level_tag = true,      // 启用级别标签
    .enable_file_line = false,     // 禁用文件信息
    .enable_function_name = false, // 禁用函数名
    .enable_color = false          // 禁用颜色（嵌入式环境）
};
```

## 6. 使用示例

### 6.1 基本初始化

```c
#include "log_module.h"

// 全局日志句柄
log_handle_t g_log_handle;

int main(void)
{
    // 系统初始化
    HAL_Init();
    SystemClock_Config();
    MX_USART2_UART_Init();  // 初始化USART2
    
    // 初始化日志模块
    log_init(&g_log_handle, NULL);  // 使用默认配置
    
    // 启动FreeRTOS
    osKernelInitialize();
    MX_FREERTOS_Init();
    
    // 启动日志模块
    log_start(&g_log_handle);
    
    osKernelStart();
    while (1) {}
}
```

### 6.2 日志输出示例

```c
void application_task(void)
{
    // 基本日志输出
    LOG_INFO("Application started");
    LOG_DEBUG("Debug information: value = %d", 42);
    
    // 错误处理
    if (sensor_init() != HAL_OK)
    {
        LOG_ERROR("Sensor initialization failed");
        return;
    }
    
    // 条件日志
    LOG_WARN_IF(battery_low(), "Battery level is low");
    
    // 性能监控
    uint32_t start_time = osKernelGetTickCount();
    process_data();
    uint32_t duration = osKernelGetTickCount() - start_time;
    LOG_INFO("Data processing completed in %lu ms", duration);
}
```

### 6.3 动态配置示例

```c
void configure_logging(void)
{
    // 在调试阶段启用详细日志
    #ifdef DEBUG
    log_set_level(&g_log_handle, LOG_LEVEL_DEBUG);
    #else
    log_set_level(&g_log_handle, LOG_LEVEL_INFO);
    #endif
    
    // 运行时调整日志级别
    if (is_production_mode())
    {
        log_set_level(&g_log_handle, LOG_LEVEL_WARN);
        LOG_INFO("Switched to production logging mode");
    }
}
```

## 7. 集成指南

### 7.1 文件集成

1. 将以下文件添加到项目中：
   - `Application/Inc/log_module.h`
   - `Application/Src/log_module.c`

2. 确保USART2已正确配置：
   - 在`Core/Inc/usart.h`中添加USART2声明
   - 在`Core/Src/usart.c`中实现USART2初始化
   - 添加printf重定向代码

3. 在项目包含路径中添加：
   - `Application/Inc`

### 7.2 依赖要求

#### 7.2.1 硬件要求
- STM32G4系列微控制器
- USART2外设（PA2/PA3引脚）
- 至少8KB RAM用于缓冲区和队列

#### 7.2.2 软件要求
- STM32CubeMX生成的HAL库项目
- FreeRTOS v10.0+
- 标准C库支持

#### 7.2.3 FreeRTOS配置

确保以下FreeRTOS功能已启用：
- 任务管理
- 消息队列
- 互斥锁
- 动态内存分配

### 7.3 编译配置

在项目的编译器定义中添加：
```c
#define USE_HAL_DRIVER
#define STM32G474xx
```

对于GCC编译器，需要启用小型printf：
- 在链接器设置中启用"Small printf"选项
- 或添加编译器标志：`-u _printf_float`（如需浮点支持）

## 8. 性能指标

### 8.1 内存使用

- **RAM使用**: ~2KB（包括缓冲区、队列和任务栈）
- **Flash使用**: ~4KB（代码和常量数据）
- **任务栈**: 512字节（可配置）

### 8.2 性能指标

- **日志处理速度**: >1000 msg/sec
- **消息延迟**: <10ms（从调用到输出）
- **CPU占用**: <5%（在115200波特率下）
- **队列容量**: 20条消息（可配置）

### 8.3 限制

- **最大消息长度**: 256字节
- **最大队列深度**: 可配置，建议不超过50
- **支持的日志级别**: 4个（DEBUG/INFO/WARN/ERROR）

## 9. 故障排除

### 9.1 常见问题

#### 9.1.1 日志无输出

**可能原因**:
- USART2未正确初始化
- printf重定向未实现
- 日志级别设置过高
- 日志模块未启动

**解决方案**:
```c
// 检查USART2状态
if (huart2.gState != HAL_UART_STATE_READY)
{
    MX_USART2_UART_Init();
}

// 检查日志模块状态
if (log_get_level(&g_log_handle) == LOG_LEVEL_NONE)
{
    log_set_level(&g_log_handle, LOG_LEVEL_DEBUG);
}
```

#### 9.1.2 消息丢失

**可能原因**:
- 队列满
- 内存不足
- 任务优先级过低

**解决方案**:
```c
// 增加队列大小
log_config_t config = {
    .queue_size = 50,  // 增加到50
    // ... 其他配置
};

// 检查统计信息
log_statistics_t stats;
log_get_statistics(&g_log_handle, &stats);
if (stats.dropped_messages > 0)
{
    LOG_WARN("Dropped %lu messages", stats.dropped_messages);
}
```

#### 9.1.3 性能问题

**可能原因**:
- 日志输出过于频繁
- 消息过长
- 串口波特率过低

**解决方案**:
```c
// 提高日志级别，减少输出
log_set_level(&g_log_handle, LOG_LEVEL_WARN);

// 使用条件日志
LOG_DEBUG_IF(debug_enabled, "Debug info: %d", value);

// 增加串口波特率（在usart.c中）
huart2.Init.BaudRate = 230400;  // 提高到230400
```

### 9.2 调试技巧

#### 9.2.1 启用详细调试

```c
// 临时启用所有日志
log_set_level(&g_log_handle, LOG_LEVEL_DEBUG);

// 输出模块状态
LOG_INFO("Log module state: %d", g_log_handle.state);
LOG_INFO("Log enabled: %s", g_log_handle.enabled ? "Yes" : "No");
```

#### 9.2.2 性能监控

```c
void monitor_log_performance(void)
{
    log_statistics_t stats;
    log_get_statistics(&g_log_handle, &stats);
    
    printf("=== Log Statistics ===\r\n");
    printf("Total messages: %lu\r\n", stats.total_messages);
    printf("DEBUG: %lu, INFO: %lu\r\n", stats.debug_count, stats.info_count);
    printf("WARN: %lu, ERROR: %lu\r\n", stats.warn_count, stats.error_count);
    printf("Dropped: %lu\r\n", stats.dropped_messages);
    printf("Buffer overflows: %lu\r\n", stats.buffer_overflows);
}
```

## 10. 版本历史

| 版本 | 日期 | 作者 | 变更说明 |
|------|------|------|----------|
| 1.0.0 | 2025-08-04 | mkx | 初始版本，完整功能实现 |

## 11. 联系信息

如有问题或建议，请联系：
- 作者：mkx
- 邮箱：[请填写实际邮箱]
- 项目地址：[请填写项目地址]

---

**注意**: 本模块专为STM32G4系列设计，使用前请确保硬件和软件环境兼容性。
