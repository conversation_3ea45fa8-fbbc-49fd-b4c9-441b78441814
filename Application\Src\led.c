#include "led.h"

#include "stm32g4xx_hal.h"

/**
 * @brief  LED初始化函数
 * @param  无
 * @retval 无
 */
void LED_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    // 使能GPIOA时钟
    __HAL_RCC_GPIOA_CLK_ENABLE();

    // 配置LED1引脚 (PA5)
    GPIO_InitStruct.Pin = LED1_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(LED1_PORT, &GPIO_InitStruct);

    // 配置LED2引脚 (PA6)
    GPIO_InitStruct.Pin = LED2_PIN;
    HAL_GPIO_Init(LED2_PORT, &GPIO_InitStruct);

    // 初始状态：关闭LED
    HAL_GPIO_WritePin(LED1_PORT, LED1_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(LED2_PORT, LED2_PIN, GPIO_PIN_RESET);
}

/**
 * @brief  LED控制函数
 * @param  led_pin: LED引脚
 * @param  led_port: LED端口
 * @param  state: LED状态 (LED_ON 或 LED_OFF)
 * @retval 无
 */
void LED_Control(uint16_t led_pin, GPIO_TypeDef *led_port, LED_StateTypeDef state)
{
    if (state == LED_ON)
    {
        HAL_GPIO_WritePin(led_port, led_pin, GPIO_PIN_SET);
    }
    else
    {
        HAL_GPIO_WritePin(led_port, led_pin, GPIO_PIN_RESET);
    }
}

/**
 * @brief  LED闪烁函数
 * @param  led_pin: LED引脚
 * @param  led_port: LED端口
 * @param  delay_ms: 闪烁延时时间(毫秒)
 * @retval 无
 */
void LED_Blink(uint16_t led_pin, GPIO_TypeDef *led_port, uint32_t delay_ms)
{
    // 点亮LED
    HAL_GPIO_WritePin(led_port, led_pin, GPIO_PIN_SET);
    HAL_Delay(delay_ms);

    // 熄灭LED
    HAL_GPIO_WritePin(led_port, led_pin, GPIO_PIN_RESET);
    HAL_Delay(delay_ms);
}

void led_task(void *argument)
{
    while (1)
    {
        LED_Blink(LED1_PIN, LED1_PORT, 1000);
        LED_Blink(LED2_PIN, LED2_PORT, 1000);
    }
}
