/**
 ******************************************************************************
 * @file    log_integration_example.c
 * @brief   日志模块集成示例
 * <AUTHOR>
 * @date    2025-08-04
 * @version 1.0.0
 ******************************************************************************
 * @attention
 *
 * 本文件展示如何在STM32G4项目中集成和使用日志模块
 *
 * 集成步骤：
 * 1. 包含必要的头文件
 * 2. 定义全局日志句柄
 * 3. 在main函数中初始化USART2和日志模块
 * 4. 启动日志模块
 * 5. 在应用代码中使用日志宏
 *
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "cmsis_os.h"
#include "gpio.h"
#include "led.h"
#include "log_module.h" // 添加日志模块头文件
#include "main.h"
#include "usart.h"

/* Private variables ---------------------------------------------------------*/
log_handle_t g_log_handle; // 全局日志模块句柄

/* Private function prototypes -----------------------------------------------*/
void test_log_functionality(void);
void demo_task(void *argument);

/**
 * @brief  The application entry point.
 * @retval int
 */
int main(void)
{
    /* USER CODE BEGIN 1 */

    /* USER CODE END 1 */

    /* MCU Configuration--------------------------------------------------------*/

    /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
    HAL_Init();

    /* USER CODE BEGIN Init */

    /* USER CODE END Init */

    /* Configure the system clock */
    SystemClock_Config();

    /* USER CODE BEGIN SysInit */

    /* USER CODE END SysInit */

    /* Initialize all configured peripherals */
    MX_GPIO_Init();
    LED_Init();
    MX_USART1_UART_Init(); // 通信模块使用的串口
    MX_USART2_UART_Init(); // 日志模块使用的串口

    /* USER CODE BEGIN 2 */

    // 测试printf重定向功能
    printf("STM32G4 System Started\r\n");
    printf("USART2 printf redirection working!\r\n");

    // 可选：初始化日志模块（需要在FreeRTOS启动后进行）
    // log_error_t log_result = log_init(&g_log_handle, NULL);

    /* USER CODE END 2 */

    /* Init scheduler */
    osKernelInitialize(); /* Call init function for freertos objects (in freertos.c) */
    MX_FREERTOS_Init();

    /* USER CODE BEGIN RTOS_MUTEX */
    /* add mutexes, ... */
    /* USER CODE END RTOS_MUTEX */

    /* USER CODE BEGIN RTOS_SEMAPHORES */
    /* add semaphores, ... */
    /* USER CODE END RTOS_SEMAPHORES */

    /* USER CODE BEGIN RTOS_TIMERS */
    /* start timers, add new ones, ... */
    /* USER CODE END RTOS_TIMERS */

    /* USER CODE BEGIN RTOS_QUEUES */
    /* add queues, ... */
    /* USER CODE END RTOS_QUEUES */

    /* Create the thread(s) */
    /* creation of defaultTask */

    /* USER CODE BEGIN RTOS_THREADS */

    // 日志模块初始化和启动应在应用任务中进行
    // 这里只创建基本的应用任务

    /* USER CODE END RTOS_THREADS */

    /* USER CODE BEGIN RTOS_EVENTS */
    /* add events, ... */
    /* USER CODE END RTOS_EVENTS */

    /* Start scheduler */
    osKernelStart();

    /* We should never get here as control is now taken by the scheduler */
    /* Infinite loop */
    /* USER CODE BEGIN WHILE */
    while (1)
    {
        /* USER CODE END WHILE */

        /* USER CODE BEGIN 3 */
    }
    /* USER CODE END 3 */
}

/**
 * @brief 简单的printf测试函数
 * <AUTHOR>
 * @date 2025-08-04
 * @note 测试printf重定向功能
 */
void test_printf_redirection(void)
{
    // 测试基本printf功能
    printf("=== Printf Redirection Test ===\r\n");
    printf("Integer: %d\r\n", 42);
    printf("String: %s\r\n", "Hello STM32G4");
    printf("Hex: 0x%08X\r\n", 0x12345678);
    printf("Float: %.2f\r\n", 3.14f);
    printf("=== Test Complete ===\r\n");
}

/**
 * @brief 日志模块使用示例
 * <AUTHOR>
 * @date 2025-08-04
 * @note 在应用任务中调用此函数来演示日志功能
 */
void log_module_example(void)
{
    // 首先测试printf重定向
    test_printf_redirection();

    // 如果需要使用日志模块，需要先初始化
    // log_error_t result = log_init(&g_log_handle, NULL);
    // if (result == LOG_OK) {
    //     log_start(&g_log_handle);
    //     LOG_INFO("Log module initialized successfully");
    // }
}
LOG_INFO("Log module version: %d.%d.%d",
         LOG_MODULE_VERSION_MAJOR,
         LOG_MODULE_VERSION_MINOR,
         LOG_MODULE_VERSION_PATCH);

while (1)
{
    counter++;

    // 演示不同级别的日志输出
    LOG_DEBUG("Debug message #%lu", counter);

    if (counter % 5 == 0)
    {
        LOG_INFO("Info message: counter = %lu", counter);
    }

    if (counter % 10 == 0)
    {
        LOG_WARN("Warning: counter reached %lu", counter);
    }

    if (counter % 20 == 0)
    {
        LOG_ERROR("Error simulation: counter = %lu", counter);

        // 获取并显示统计信息
        log_statistics_t stats;
        if (log_get_statistics(&g_log_handle, &stats) == LOG_OK)
        {
            LOG_INFO("Log statistics:");
            LOG_INFO("  Total messages: %lu", stats.total_messages);
            LOG_INFO("  DEBUG: %lu, INFO: %lu, WARN: %lu, ERROR: %lu",
                     stats.debug_count, stats.info_count,
                     stats.warn_count, stats.error_count);
            LOG_INFO("  Dropped messages: %lu", stats.dropped_messages);
        }
    }

    // 演示条件日志输出
    LOG_INFO_IF(counter % 15 == 0, "Conditional log: counter is divisible by 15");

    // 演示简化的日志输出（不带文件信息）
    if (counter % 25 == 0)
    {
        LOGI("Simple log message without file info");
    }

    // 演示printf重定向功能
    if (counter % 30 == 0)
    {
        printf("Direct printf output: counter = %lu\r\n", counter);
    }

    // 控制LED闪烁
    LED_Control(LED1_PIN, LED1_PORT, (counter % 2) ? LED_ON : LED_OFF);

    osDelay(1000); // 1秒延时
}
}

/**
 * @brief 测试日志功能
 * <AUTHOR>
 * @date 2025-08-04
 * @note 可在其他地方调用此函数测试日志功能
 */
void test_log_functionality(void)
{
    LOG_DEBUG("This is a debug message");
    LOG_INFO("This is an info message");
    LOG_WARN("This is a warning message");
    LOG_ERROR("This is an error message");

    // 测试格式化输出
    int value = 42;
    float temperature = 25.6f;
    const char *status = "OK";

    LOG_INFO("Sensor data: value=%d, temp=%.1f°C, status=%s",
             value, temperature, status);

    // 测试长消息
    LOG_DEBUG("This is a very long debug message that demonstrates how the log module handles messages that might exceed normal length expectations and ensures proper formatting");

    // 测试不同日志级别的过滤
    log_level_t original_level = log_get_level(&g_log_handle);

    LOG_INFO("Setting log level to WARN - DEBUG and INFO messages will be filtered");
    log_set_level(&g_log_handle, LOG_LEVEL_WARN);

    LOG_DEBUG("This debug message should not appear");
    LOG_INFO("This info message should not appear");
    LOG_WARN("This warning message should appear");
    LOG_ERROR("This error message should appear");

    // 恢复原始日志级别
    log_set_level(&g_log_handle, original_level);
    LOG_INFO("Log level restored to original setting");
}

/**
 * @brief 错误处理函数示例
 * <AUTHOR>
 * @date 2025-08-04
 * @param error_code 错误码
 * @note 演示在错误处理中使用日志
 */
void handle_application_error(uint32_t error_code)
{
    LOG_ERROR("Application error occurred: code=0x%08lX", error_code);

    switch (error_code)
    {
    case 0x1001:
        LOG_ERROR("Hardware initialization failed");
        break;
    case 0x1002:
        LOG_ERROR("Communication timeout");
        break;
    case 0x1003:
        LOG_ERROR("Memory allocation failed");
        break;
    default:
        LOG_ERROR("Unknown error code");
        break;
    }

    // 记录系统状态
    LOG_INFO("System uptime: %lu ms", osKernelGetTickCount());

    // 获取日志统计信息
    log_statistics_t stats;
    if (log_get_statistics(&g_log_handle, &stats) == LOG_OK)
    {
        LOG_INFO("Error count before this error: %lu", stats.error_count - 1);
    }
}

/**
 * @brief 性能测试函数
 * <AUTHOR>
 * @date 2025-08-04
 * @note 测试日志模块的性能
 */
void test_log_performance(void)
{
    uint32_t start_time = osKernelGetTickCount();
    const uint32_t test_count = 100;

    LOG_INFO("Starting log performance test with %lu messages", test_count);

    for (uint32_t i = 0; i < test_count; i++)
    {
        LOG_DEBUG("Performance test message #%lu", i);
    }

    uint32_t end_time = osKernelGetTickCount();
    uint32_t duration = end_time - start_time;

    LOG_INFO("Performance test completed:");
    LOG_INFO("  Messages: %lu", test_count);
    LOG_INFO("  Duration: %lu ms", duration);
    LOG_INFO("  Rate: %.1f msg/sec", (float)test_count * 1000.0f / duration);
}

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
