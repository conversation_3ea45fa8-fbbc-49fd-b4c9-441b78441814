/**
 ******************************************************************************
 * @file    simple_log_example.c
 * @brief   简单的日志模块使用示例
 * <AUTHOR>
 * @date    2025-08-04
 * @version 1.0.0
 ******************************************************************************
 * @attention
 * 
 * 本文件提供简单的日志模块使用示例，避免复杂的集成问题
 * 
 * 使用步骤：
 * 1. 确保USART2已正确初始化
 * 2. 在main函数中调用test_printf_redirection()测试printf重定向
 * 3. 在FreeRTOS任务中使用日志模块功能
 * 
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "usart.h"
#include "log_module.h"
#include <stdio.h>

/* Global variables ----------------------------------------------------------*/
log_handle_t g_log_handle;  // 全局日志模块句柄

/**
 * @brief 测试printf重定向功能
 * <AUTHOR>
 * @date 2025-08-04
 * @note 在main函数中调用此函数测试printf重定向
 */
void test_printf_redirection(void)
{
    printf("\r\n=== Printf Redirection Test ===\r\n");
    printf("STM32G4 System Started\r\n");
    printf("Integer: %d\r\n", 42);
    printf("String: %s\r\n", "Hello STM32G4");
    printf("Hex: 0x%08X\r\n", 0x12345678);
    printf("Float: %.2f\r\n", 3.14f);
    printf("=== Test Complete ===\r\n\r\n");
}

/**
 * @brief 简单的日志模块测试
 * <AUTHOR>
 * @date 2025-08-04
 * @note 在FreeRTOS任务中调用此函数测试日志模块
 */
void simple_log_test(void)
{
    // 初始化日志模块
    log_error_t result = log_init(&g_log_handle, NULL);
    if (result != LOG_OK)
    {
        printf("Log init failed: %d\r\n", result);
        return;
    }
    
    // 启动日志模块
    result = log_start(&g_log_handle);
    if (result != LOG_OK)
    {
        printf("Log start failed: %d\r\n", result);
        return;
    }
    
    // 等待日志模块启动
    osDelay(100);
    
    // 测试基本日志输出
    LOG_INFO("Log module test started");
    LOG_DEBUG("Debug message test");
    LOG_WARN("Warning message test");
    LOG_ERROR("Error message test");
    
    // 测试格式化输出
    LOG_INFO("Formatted output: value=%d, temp=%.1f", 123, 25.6f);
    
    // 测试简化宏
    LOGI("Simple info message");
    LOGW("Simple warning message");
    
    printf("Log module test completed\r\n");
}

/**
 * @brief 应用任务示例
 * <AUTHOR>
 * @date 2025-08-04
 * @param argument 任务参数
 * @note 完整的应用任务示例
 */
void app_task(void *argument)
{
    uint32_t counter = 0;
    
    // 等待系统稳定
    osDelay(1000);
    
    // 测试日志模块
    simple_log_test();
    
    // 主循环
    while (1)
    {
        counter++;
        
        // 每10次循环输出一次日志
        if (counter % 10 == 0)
        {
            LOG_INFO("Task running, counter = %lu", counter);
        }
        
        // 每50次循环模拟一次错误
        if (counter % 50 == 0)
        {
            LOG_ERROR("Simulated error at counter = %lu", counter);
        }
        
        osDelay(1000);  // 1秒延时
    }
}

/**
 * @brief 在main.c中的集成示例
 * <AUTHOR>
 * @date 2025-08-04
 * @note 展示如何在main.c中集成这些功能
 */
/*
// 在main.c中添加以下代码：

#include "simple_log_example.c"  // 包含此文件

int main(void)
{
    // 系统初始化
    HAL_Init();
    SystemClock_Config();
    
    // 外设初始化
    MX_GPIO_Init();
    MX_USART1_UART_Init();  // 通信模块
    MX_USART2_UART_Init();  // 日志模块
    
    // 测试printf重定向
    test_printf_redirection();
    
    // FreeRTOS初始化
    osKernelInitialize();
    MX_FREERTOS_Init();
    
    // 创建应用任务
    const osThreadAttr_t app_task_attr = {
        .name = "AppTask",
        .stack_size = 512,
        .priority = (osPriority_t)osPriorityNormal
    };
    osThreadNew(app_task, NULL, &app_task_attr);
    
    // 启动调度器
    osKernelStart();
    
    while (1) {}
}
*/

/**
 * @brief 错误处理示例
 * <AUTHOR>
 * @date 2025-08-04
 * @param error_code 错误码
 */
void error_handler_with_log(uint32_t error_code)
{
    // 紧急情况使用printf
    printf("CRITICAL ERROR: 0x%08lX\r\n", error_code);
    
    // 如果日志模块可用，记录详细信息
    if (g_log_handle.initialized)
    {
        LOG_ERROR("System error: code=0x%08lX", error_code);
    }
}

/**
 * @brief 配置示例
 * <AUTHOR>
 * @date 2025-08-04
 */
void log_config_example(void)
{
    // 设置日志级别
    log_set_level(&g_log_handle, LOG_LEVEL_INFO);  // 只输出INFO及以上
    
    // 启用/禁用日志
    log_enable(&g_log_handle, true);
    
    // 获取统计信息
    log_statistics_t stats;
    if (log_get_statistics(&g_log_handle, &stats) == LOG_OK)
    {
        printf("Log statistics:\r\n");
        printf("  Total: %lu\r\n", stats.total_messages);
        printf("  Errors: %lu\r\n", stats.error_count);
    }
}

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
