# STM32G4 串口通信协议技术文档

**版本**: 1.0.0  
**作者**: mkx  
**日期**: 2025-08-04  
**适用平台**: STM32G4系列微控制器

## 1. 概述

本文档描述了STM32G4项目中串口通信模块的完整协议规范。该协议设计用于实现可靠的串口通信，支持命令解析、响应机制和错误处理。

### 1.1 设计目标

- **可靠性**: 提供完整的错误检测和处理机制
- **扩展性**: 支持用户自定义命令和协议扩展
- **高效性**: 优化的数据包结构，减少传输开销
- **易用性**: 简洁的API接口，便于集成和使用

### 1.2 主要特性

- 基于FreeRTOS的多任务处理
- 完整的协议栈实现
- 自动重试和超时处理
- 统计信息收集
- 内置命令支持
- 用户回调机制

## 2. 协议规范

### 2.1 数据包结构

通信协议采用固定格式的数据包结构：

```
+--------+--------+--------+--------+--------+--------+--------+
| 帧头   | 命令   | 数据   | 有效载荷        | 校验码 | 帧尾   |
| (2B)   | 类型   | 长度   | (0-255B)       | (1B)   | (2B)   |
|        | (1B)   | (1B)   |                |        |        |
+--------+--------+--------+--------+--------+--------+--------+
```

#### 字段说明

| 字段 | 长度 | 值 | 说明 |
|------|------|----|----- |
| 帧头 | 2字节 | 0xAA55 | 数据包起始标识 |
| 命令类型 | 1字节 | 见命令表 | 指定命令类型 |
| 数据长度 | 1字节 | 0-255 | 有效载荷长度 |
| 有效载荷 | 0-255字节 | 变长 | 实际数据内容 |
| 校验码 | 1字节 | 计算值 | 简单累加校验 |
| 帧尾 | 2字节 | 0x55AA | 数据包结束标识 |

### 2.2 校验机制

采用简单累加校验算法：
- 校验范围：命令类型 + 数据长度 + 有效载荷
- 计算方法：所有字节累加后取低8位

```c
uint8_t checksum = 0;
for (int i = 0; i < len; i++) {
    checksum += data[i];
}
```

### 2.3 命令类型定义

| 命令码 | 命令名称 | 说明 | 数据长度 |
|--------|----------|------|----------|
| 0x01 | PING | 心跳命令 | 0 |
| 0x02 | ACK | 应答命令 | 1 |
| 0x03 | NACK | 否定应答 | 2 |
| 0x10 | GET_VERSION | 获取版本信息 | 0 |
| 0x11 | GET_STATUS | 获取状态信息 | 0 |
| 0x20 | SET_CONFIG | 设置配置参数 | 变长 |
| 0x21 | GET_CONFIG | 获取配置参数 | 变长 |
| 0x30 | RESET | 复位命令 | 0 |
| 0x40 | LED_CONTROL | LED控制命令 | 2 |
| 0x80+ | USER_DEFINED | 用户自定义命令 | 变长 |

## 3. 错误码定义

| 错误码 | 错误名称 | 说明 |
|--------|----------|------|
| 0x00 | COMM_OK | 成功 |
| 0x01 | COMM_ERROR | 一般错误 |
| 0x02 | COMM_ERROR_PARAM | 参数错误 |
| 0x03 | COMM_ERROR_TIMEOUT | 超时错误 |
| 0x04 | COMM_ERROR_CHECKSUM | 校验错误 |
| 0x05 | COMM_ERROR_FRAME | 帧格式错误 |
| 0x06 | COMM_ERROR_BUFFER_FULL | 缓冲区满 |
| 0x07 | COMM_ERROR_NOT_INIT | 未初始化 |
| 0x08 | COMM_ERROR_BUSY | 忙碌状态 |
| 0x09 | COMM_ERROR_NO_MEMORY | 内存不足 |
| 0x0A | COMM_ERROR_UNSUPPORTED | 不支持的操作 |

## 4. 通信流程

### 4.1 基本通信流程

```mermaid
sequenceDiagram
    participant Host as 上位机
    participant MCU as STM32G4
    
    Host->>MCU: 发送命令
    MCU->>MCU: 解析命令
    MCU->>MCU: 处理命令
    MCU->>Host: 发送响应/ACK
    
    Note over Host,MCU: 正常通信流程
```

### 4.2 错误处理流程

```mermaid
sequenceDiagram
    participant Host as 上位机
    participant MCU as STM32G4
    
    Host->>MCU: 发送命令
    MCU->>MCU: 检测错误
    MCU->>Host: 发送NACK + 错误码
    Host->>Host: 处理错误
    Host->>MCU: 重新发送命令
    
    Note over Host,MCU: 错误处理流程
```

## 5. API接口说明

### 5.1 初始化函数

```c
comm_error_t comm_init(comm_handle_t *handle, 
                      UART_HandleTypeDef *huart, 
                      const comm_uart_config_t *config);
```

**功能**: 初始化通信模块  
**参数**:
- `handle`: 通信模块句柄
- `huart`: UART句柄
- `config`: 串口配置参数（可为NULL使用默认配置）

**返回值**: 错误码

### 5.2 启动函数

```c
comm_error_t comm_start(comm_handle_t *handle);
```

**功能**: 启动通信模块，创建接收和发送任务  
**参数**: `handle` - 通信模块句柄  
**返回值**: 错误码

### 5.3 发送函数

```c
comm_error_t comm_send_data(comm_handle_t *handle, 
                           comm_cmd_type_t cmd_type,
                           const uint8_t *data, 
                           uint8_t len, 
                           uint32_t timeout);
```

**功能**: 发送数据  
**参数**:
- `handle`: 通信模块句柄
- `cmd_type`: 命令类型
- `data`: 数据指针
- `len`: 数据长度
- `timeout`: 超时时间(ms)

**返回值**: 错误码

### 5.4 回调注册

```c
comm_error_t comm_register_callback(comm_handle_t *handle, 
                                   comm_cmd_callback_t callback);
```

**功能**: 注册命令处理回调函数  
**参数**:
- `handle`: 通信模块句柄
- `callback`: 回调函数指针

**返回值**: 错误码

## 6. 使用示例

### 6.1 基本初始化

```c
#include "comm_module.h"
#include "usart.h"

// 全局通信句柄
comm_handle_t g_comm_handle;

// 用户命令回调函数
comm_error_t user_cmd_callback(comm_cmd_type_t cmd_type, 
                              const uint8_t *data, 
                              uint8_t len,
                              uint8_t *response_data, 
                              uint8_t *response_len)
{
    switch (cmd_type)
    {
    case COMM_CMD_LED_CONTROL:
        if (len >= 2)
        {
            uint8_t led_id = data[0];
            uint8_t led_state = data[1];
            // 控制LED
            LED_Control(led_id == 1 ? LED1_PIN : LED2_PIN, 
                       led_id == 1 ? LED1_PORT : LED2_PORT,
                       led_state ? LED_ON : LED_OFF);
            *response_len = 0;
            return COMM_OK;
        }
        return COMM_ERROR_PARAM;
        
    default:
        return COMM_ERROR_UNSUPPORTED;
    }
}

int main(void)
{
    // 系统初始化
    HAL_Init();
    SystemClock_Config();
    MX_GPIO_Init();
    MX_USART1_UART_Init();
    
    // 初始化通信模块
    comm_error_t result = comm_init(&g_comm_handle, &huart1, NULL);
    if (result != COMM_OK)
    {
        Error_Handler();
    }
    
    // 注册回调函数
    comm_register_callback(&g_comm_handle, user_cmd_callback);
    
    // 启动FreeRTOS
    osKernelInitialize();
    MX_FREERTOS_Init();
    
    // 启动通信模块
    comm_start(&g_comm_handle);
    
    osKernelStart();
    
    while (1) {}
}
```

### 6.2 发送心跳命令

```c
// 发送心跳命令
comm_error_t send_ping(void)
{
    return comm_send_data(&g_comm_handle, COMM_CMD_PING, NULL, 0, 1000);
}
```

### 6.3 获取版本信息

```c
// 获取模块版本
comm_error_t get_version(void)
{
    return comm_send_data(&g_comm_handle, COMM_CMD_GET_VERSION, NULL, 0, 1000);
}
```

### 6.4 LED控制示例

```c
// 控制LED
comm_error_t control_led(uint8_t led_id, uint8_t state)
{
    uint8_t data[2] = {led_id, state};
    return comm_send_data(&g_comm_handle, COMM_CMD_LED_CONTROL, data, 2, 1000);
}
```

## 7. 集成指南

### 7.1 文件集成

1. 将以下文件添加到项目中：
   - `Application/Inc/comm_module.h`
   - `Application/Src/comm_module.c`

2. 在项目包含路径中添加：
   - `Application/Inc`

3. 确保项目已包含以下依赖：
   - STM32 HAL库
   - FreeRTOS

### 7.2 配置要求

#### 7.2.1 UART配置

- 波特率：115200（默认，可配置）
- 数据位：8位
- 停止位：1位
- 奇偶校验：无
- 流控制：无

#### 7.2.2 FreeRTOS配置

确保以下FreeRTOS功能已启用：
- 任务管理
- 消息队列
- 互斥锁
- 软件定时器

#### 7.2.3 内存要求

- 堆内存：至少4KB（用于FreeRTOS对象）
- 栈内存：每个任务512字节

### 7.3 编译配置

在项目的编译器定义中添加：
```c
#define USE_HAL_DRIVER
#define STM32G474xx
```

### 7.4 中断配置

需要在`stm32g4xx_it.c`中添加UART接收完成回调：

```c
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart == &huart1)  // 根据实际使用的UART调整
    {
        // 处理接收完成
        extern comm_handle_t g_comm_handle;
        g_comm_handle.rx_index++;
        if (g_comm_handle.rx_index < COMM_RX_BUFFER_SIZE)
        {
            HAL_UART_Receive_IT(huart, &g_comm_handle.rx_buffer[g_comm_handle.rx_index], 1);
        }
        else
        {
            g_comm_handle.rx_index = 0;
            HAL_UART_Receive_IT(huart, &g_comm_handle.rx_buffer[0], 1);
        }
    }
}
```

## 8. 测试和调试

### 8.1 测试工具

推荐使用以下工具进行测试：
- 串口调试助手
- Python脚本
- 示波器（用于时序分析）

### 8.2 测试用例

#### 8.2.1 基本通信测试

发送心跳命令：
```
发送: AA 55 01 00 01 55 AA
期望: AA 55 02 01 01 04 55 AA
```

#### 8.2.2 版本信息测试

获取版本信息：
```
发送: AA 55 10 00 10 55 AA
期望: AA 55 10 03 01 00 00 24 55 AA
```

#### 8.2.3 LED控制测试

控制LED1开启：
```
发送: AA 55 40 02 01 01 84 55 AA
期望: AA 55 02 01 40 43 55 AA
```

### 8.3 常见问题

#### 8.3.1 通信无响应

- 检查UART配置是否正确
- 确认FreeRTOS任务是否正常启动
- 验证中断是否正确配置

#### 8.3.2 校验错误

- 确认校验算法实现正确
- 检查数据传输是否有丢失或错误

#### 8.3.3 内存不足

- 增加堆内存大小
- 检查任务栈大小配置

## 9. 性能指标

### 9.1 传输性能

- 最大传输速率：115200 bps
- 最大数据包长度：262字节
- 最小数据包长度：7字节
- 协议开销：7字节

### 9.2 实时性

- 命令响应时间：< 10ms
- 任务切换延迟：< 1ms
- 中断响应时间：< 100μs

### 9.3 可靠性

- 错误检测率：99.9%
- 自动重试次数：3次
- 超时处理：可配置

## 10. 版本历史

| 版本 | 日期 | 作者 | 变更说明 |
|------|------|------|----------|
| 1.0.0 | 2025-08-04 | mkx | 初始版本，完整协议实现 |

## 11. 联系信息

如有问题或建议，请联系：
- 作者：mkx
- 邮箱：[请填写实际邮箱]
- 项目地址：[请填写项目地址]

---

**注意**: 本文档基于STM32G4系列微控制器设计，使用前请确保硬件平台兼容性。
```
