/**
 ******************************************************************************
 * @file    comm_module.c
 * @brief   串口通信模块实现文件
 * <AUTHOR>
 * @version V1.0.0
 * @date    2025-08-04
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "comm_module.h"
#include "stm32g4xx_hal.h"
#include <stdio.h>
#include <stdlib.h>

/* Private defines -----------------------------------------------------------*/
#define COMM_TASK_STACK_SIZE 512
#define COMM_TASK_PRIORITY (tskIDLE_PRIORITY + 1)
#define COMM_QUEUE_TIMEOUT 100
#define COMM_MUTEX_TIMEOUT 1000

/* Private variables ---------------------------------------------------------*/
static const char *error_strings[] = {
    "Success",
    "General error",
    "Parameter error",
    "Timeout error",
    "Checksum error",
    "Frame format error",
    "Buffer full",
    "Not initialized",
    "Busy",
    "No memory",
    "Unsupported operation"};

/* Private function prototypes -----------------------------------------------*/
static void comm_rx_task(void *argument);
static void comm_tx_task(void *argument);
static void comm_heartbeat_timer_callback(TimerHandle_t xTimer);
static void comm_timeout_timer_callback(TimerHandle_t xTimer);
static comm_error_t comm_process_received_frame(comm_handle_t *handle, const uint8_t *frame, uint16_t len);
static comm_error_t comm_build_frame(const comm_command_t *cmd, uint8_t *frame, uint16_t *frame_len);
static void comm_change_state(comm_handle_t *handle, comm_state_t new_state);
static void comm_call_rx_callback(comm_handle_t *handle, comm_cmd_type_t cmd_type, uint8_t *data, uint16_t len);

/* Public functions ----------------------------------------------------------*/

/**
 * @brief 初始化通信模块
 */
comm_error_t comm_init(comm_handle_t *handle, const comm_config_t *config)
{
    if (handle == NULL || config == NULL || config->huart == NULL)
    {
        return COMM_ERROR_PARAM;
    }

    if (handle->initialized)
    {
        return COMM_ERROR_BUSY;
    }

    // 清零句柄结构
    memset(handle, 0, sizeof(comm_handle_t));

    // 复制配置
    memcpy(&handle->config, config, sizeof(comm_config_t));

    // 创建FreeRTOS对象
    handle->cmd_queue = xQueueCreate(COMM_CMD_QUEUE_SIZE, sizeof(comm_command_t));
    if (handle->cmd_queue == NULL)
    {
        return COMM_ERROR_NO_MEMORY;
    }

    handle->rx_queue = xQueueCreate(COMM_RX_BUFFER_SIZE, sizeof(uint8_t));
    if (handle->rx_queue == NULL)
    {
        vQueueDelete(handle->cmd_queue);
        return COMM_ERROR_NO_MEMORY;
    }

    handle->tx_mutex = xSemaphoreCreateMutex();
    if (handle->tx_mutex == NULL)
    {
        vQueueDelete(handle->cmd_queue);
        vQueueDelete(handle->rx_queue);
        return COMM_ERROR_NO_MEMORY;
    }

    handle->config_mutex = xSemaphoreCreateMutex();
    if (handle->config_mutex == NULL)
    {
        vQueueDelete(handle->cmd_queue);
        vQueueDelete(handle->rx_queue);
        vSemaphoreDelete(handle->tx_mutex);
        return COMM_ERROR_NO_MEMORY;
    }

    // 创建心跳定时器
    if (config->enable_heartbeat)
    {
        handle->heartbeat_timer = xTimerCreate("CommHeartbeat",
                                               pdMS_TO_TICKS(config->heartbeat_interval),
                                               pdTRUE,
                                               handle,
                                               comm_heartbeat_timer_callback);
        if (handle->heartbeat_timer == NULL)
        {
            vQueueDelete(handle->cmd_queue);
            vQueueDelete(handle->rx_queue);
            vSemaphoreDelete(handle->tx_mutex);
            vSemaphoreDelete(handle->config_mutex);
            return COMM_ERROR_NO_MEMORY;
        }
    }

    // 创建超时定时器
    handle->timeout_timer = xTimerCreate("CommTimeout",
                                         pdMS_TO_TICKS(config->timeout_ms),
                                         pdFALSE,
                                         handle,
                                         comm_timeout_timer_callback);
    if (handle->timeout_timer == NULL)
    {
        vQueueDelete(handle->cmd_queue);
        vQueueDelete(handle->rx_queue);
        vSemaphoreDelete(handle->tx_mutex);
        vSemaphoreDelete(handle->config_mutex);
        if (handle->heartbeat_timer)
        {
            xTimerDelete(handle->heartbeat_timer, 0);
        }
        return COMM_ERROR_NO_MEMORY;
    }

    // 初始化统计信息
    memset(&handle->stats, 0, sizeof(comm_statistics_t));

    // 设置初始状态
    handle->initialized = true;
    comm_change_state(handle, COMM_STATE_INITIALIZED);

    return COMM_OK;
}

/**
 * @brief 反初始化通信模块
 */
comm_error_t comm_deinit(comm_handle_t *handle)
{
    if (handle == NULL || !handle->initialized)
    {
        return COMM_ERROR_NOT_INIT;
    }

    // 停止模块
    comm_stop(handle);

    // 删除FreeRTOS对象
    if (handle->cmd_queue)
    {
        vQueueDelete(handle->cmd_queue);
    }
    if (handle->rx_queue)
    {
        vQueueDelete(handle->rx_queue);
    }
    if (handle->tx_mutex)
    {
        vSemaphoreDelete(handle->tx_mutex);
    }
    if (handle->config_mutex)
    {
        vSemaphoreDelete(handle->config_mutex);
    }
    if (handle->heartbeat_timer)
    {
        xTimerDelete(handle->heartbeat_timer, 0);
    }
    if (handle->timeout_timer)
    {
        xTimerDelete(handle->timeout_timer, 0);
    }

    // 清零句柄
    memset(handle, 0, sizeof(comm_handle_t));

    return COMM_OK;
}

/**
 * @brief 启动通信模块
 */
comm_error_t comm_start(comm_handle_t *handle)
{
    if (handle == NULL || !handle->initialized)
    {
        return COMM_ERROR_NOT_INIT;
    }

    if (handle->state == COMM_STATE_RUNNING)
    {
        return COMM_OK;
    }

    // 创建接收任务
    BaseType_t result = xTaskCreate(comm_rx_task,
                                    "CommRxTask",
                                    COMM_TASK_STACK_SIZE,
                                    handle,
                                    COMM_TASK_PRIORITY,
                                    &handle->rx_task_handle);
    if (result != pdPASS)
    {
        return COMM_ERROR_NO_MEMORY;
    }

    // 创建发送任务
    result = xTaskCreate(comm_tx_task,
                         "CommTxTask",
                         COMM_TASK_STACK_SIZE,
                         handle,
                         COMM_TASK_PRIORITY,
                         &handle->tx_task_handle);
    if (result != pdPASS)
    {
        vTaskDelete(handle->rx_task_handle);
        return COMM_ERROR_NO_MEMORY;
    }

    // 启动心跳定时器
    if (handle->heartbeat_timer && handle->config.enable_heartbeat)
    {
        xTimerStart(handle->heartbeat_timer, 0);
    }

    // 启动UART接收中断
    HAL_UART_Receive_IT(handle->config.huart, &handle->rx_buffer[0], 1);

    comm_change_state(handle, COMM_STATE_RUNNING);

    return COMM_OK;
}

/**
 * @brief 停止通信模块
 */
comm_error_t comm_stop(comm_handle_t *handle)
{
    if (handle == NULL || !handle->initialized)
    {
        return COMM_ERROR_NOT_INIT;
    }

    if (handle->state != COMM_STATE_RUNNING)
    {
        return COMM_OK;
    }

    // 停止定时器
    if (handle->heartbeat_timer)
    {
        xTimerStop(handle->heartbeat_timer, 0);
    }
    if (handle->timeout_timer)
    {
        xTimerStop(handle->timeout_timer, 0);
    }

    // 停止UART
    HAL_UART_Abort_IT(handle->config.huart);

    // 删除任务
    if (handle->rx_task_handle)
    {
        vTaskDelete(handle->rx_task_handle);
        handle->rx_task_handle = NULL;
    }
    if (handle->tx_task_handle)
    {
        vTaskDelete(handle->tx_task_handle);
        handle->tx_task_handle = NULL;
    }

    comm_change_state(handle, COMM_STATE_INITIALIZED);

    return COMM_OK;
}

/**
 * @brief 发送命令
 */
comm_error_t comm_send_command(comm_handle_t *handle, const comm_command_t *cmd)
{
    if (handle == NULL || cmd == NULL || !handle->initialized)
    {
        return COMM_ERROR_PARAM;
    }

    if (handle->state != COMM_STATE_RUNNING)
    {
        return COMM_ERROR_NOT_INIT;
    }

    // 将命令加入队列
    BaseType_t result = xQueueSend(handle->cmd_queue, cmd, pdMS_TO_TICKS(COMM_QUEUE_TIMEOUT));
    if (result != pdPASS)
    {
        return COMM_ERROR_BUFFER_FULL;
    }

    return COMM_OK;
}

/**
 * @brief 发送数据
 */
comm_error_t comm_send_data(comm_handle_t *handle, comm_cmd_type_t cmd_type,
                            const uint8_t *data, uint16_t len, uint32_t timeout_ms)
{
    if (handle == NULL || (data == NULL && len > 0))
    {
        return COMM_ERROR_PARAM;
    }

    if (len > COMM_MAX_PAYLOAD_SIZE)
    {
        return COMM_ERROR_PARAM;
    }

    comm_command_t cmd = {
        .cmd_type = cmd_type,
        .priority = COMM_PRIORITY_NORMAL,
        .data = (uint8_t *)data,
        .data_len = len,
        .timeout_ms = timeout_ms,
        .user_data = NULL};

    return comm_send_command(handle, &cmd);
}

/**
 * @brief 注册接收回调函数
 */
comm_error_t comm_register_rx_callback(comm_handle_t *handle, comm_cmd_type_t cmd_type,
                                       comm_rx_callback_t callback, void *user_data)
{
    if (handle == NULL || callback == NULL || !handle->initialized)
    {
        return COMM_ERROR_PARAM;
    }

    if (handle->callback_count >= COMM_MAX_CALLBACKS)
    {
        return COMM_ERROR_BUFFER_FULL;
    }

    // 检查是否已经注册
    for (uint8_t i = 0; i < handle->callback_count; i++)
    {
        if (handle->rx_callbacks[i].cmd_type == cmd_type)
        {
            // 更新现有回调
            handle->rx_callbacks[i].callback = callback;
            handle->rx_callbacks[i].user_data = user_data;
            return COMM_OK;
        }
    }

    // 添加新回调
    handle->rx_callbacks[handle->callback_count].cmd_type = cmd_type;
    handle->rx_callbacks[handle->callback_count].callback = callback;
    handle->rx_callbacks[handle->callback_count].user_data = user_data;
    handle->callback_count++;

    return COMM_OK;
}

/**
 * @brief 注销接收回调函数
 */
comm_error_t comm_unregister_rx_callback(comm_handle_t *handle, comm_cmd_type_t cmd_type)
{
    if (handle == NULL || !handle->initialized)
    {
        return COMM_ERROR_PARAM;
    }

    // 查找并移除回调
    for (uint8_t i = 0; i < handle->callback_count; i++)
    {
        if (handle->rx_callbacks[i].cmd_type == cmd_type)
        {
            // 移动后续元素
            for (uint8_t j = i; j < handle->callback_count - 1; j++)
            {
                handle->rx_callbacks[j] = handle->rx_callbacks[j + 1];
            }
            handle->callback_count--;
            return COMM_OK;
        }
    }

    return COMM_ERROR;
}

/**
 * @brief 获取模块状态
 */
comm_state_t comm_get_state(const comm_handle_t *handle)
{
    if (handle == NULL)
    {
        return COMM_STATE_UNINITIALIZED;
    }

    return handle->state;
}

/**
 * @brief 获取统计信息
 */
comm_error_t comm_get_statistics(const comm_handle_t *handle, comm_statistics_t *stats)
{
    if (handle == NULL || stats == NULL || !handle->initialized)
    {
        return COMM_ERROR_PARAM;
    }

    memcpy(stats, &handle->stats, sizeof(comm_statistics_t));

    return COMM_OK;
}

/**
 * @brief 清除统计信息
 */
comm_error_t comm_clear_statistics(comm_handle_t *handle)
{
    if (handle == NULL || !handle->initialized)
    {
        return COMM_ERROR_PARAM;
    }

    memset(&handle->stats, 0, sizeof(comm_statistics_t));

    return COMM_OK;
}

/**
 * @brief 发送心跳包
 */
comm_error_t comm_send_heartbeat(comm_handle_t *handle)
{
    if (handle == NULL || !handle->initialized)
    {
        return COMM_ERROR_PARAM;
    }

    return comm_send_data(handle, COMM_CMD_PING, NULL, 0, handle->config.timeout_ms);
}

/**
 * @brief 发送ACK应答
 */
comm_error_t comm_send_ack(comm_handle_t *handle, comm_cmd_type_t cmd_type)
{
    if (handle == NULL || !handle->initialized)
    {
        return COMM_ERROR_PARAM;
    }

    uint8_t ack_data = cmd_type;
    return comm_send_data(handle, COMM_CMD_ACK, &ack_data, 1, handle->config.timeout_ms);
}

/**
 * @brief 发送NACK应答
 */
comm_error_t comm_send_nack(comm_handle_t *handle, comm_cmd_type_t cmd_type, comm_error_t error_code)
{
    if (handle == NULL || !handle->initialized)
    {
        return COMM_ERROR_PARAM;
    }

    uint8_t nack_data[2] = {cmd_type, error_code};
    return comm_send_data(handle, COMM_CMD_NACK, nack_data, 2, handle->config.timeout_ms);
}

/**
 * @brief 计算校验码
 */
uint8_t comm_calculate_checksum(const uint8_t *data, uint16_t len)
{
    if (data == NULL || len == 0)
    {
        return 0;
    }

    uint8_t checksum = 0;
    for (uint16_t i = 0; i < len; i++)
    {
        checksum ^= data[i];
    }

    return checksum;
}

/**
 * @brief 验证数据帧
 */
bool comm_validate_frame(const uint8_t *frame, uint16_t len)
{
    if (frame == NULL || len < COMM_FRAME_MIN_SIZE)
    {
        return false;
    }

    // 检查帧头
    uint16_t header = (frame[0] << 8) | frame[1];
    if (header != COMM_FRAME_HEADER)
    {
        return false;
    }

    // 检查数据长度
    uint8_t data_len = frame[COMM_OFFSET_DATA_LEN];
    if (data_len > COMM_MAX_PAYLOAD_SIZE)
    {
        return false;
    }

    // 计算期望的帧长度
    uint16_t expected_len = COMM_OFFSET_PAYLOAD + data_len + 1 + 2; // +1校验码 +2帧尾
    if (len != expected_len)
    {
        return false;
    }

    // 检查帧尾
    uint16_t tail = (frame[len - 2] << 8) | frame[len - 1];
    if (tail != COMM_FRAME_TAIL)
    {
        return false;
    }

    // 检查校验码
    uint8_t calculated_checksum = comm_calculate_checksum(&frame[COMM_OFFSET_CMD_TYPE], data_len + 2);
    uint8_t received_checksum = frame[COMM_OFFSET_PAYLOAD + data_len];
    if (calculated_checksum != received_checksum)
    {
        return false;
    }

    return true;
}

/**
 * @brief 获取错误描述字符串
 */
const char *comm_get_error_string(comm_error_t error)
{
    if (error >= sizeof(error_strings) / sizeof(error_strings[0]))
    {
        return "Unknown error";
    }

    return error_strings[error];
}

/**
 * @brief 获取模块版本信息
 */
void comm_get_version(uint8_t *major, uint8_t *minor, uint8_t *patch)
{
    if (major)
        *major = COMM_MODULE_VERSION_MAJOR;
    if (minor)
        *minor = COMM_MODULE_VERSION_MINOR;
    if (patch)
        *patch = COMM_MODULE_VERSION_PATCH;
}

/* Private functions ---------------------------------------------------------*/

/**
 * @brief 接收任务
 */
static void comm_rx_task(void *argument)
{
    comm_handle_t *handle = (comm_handle_t *)argument;
    uint8_t rx_byte;
    uint8_t frame_buffer[COMM_MAX_FRAME_SIZE];
    uint16_t frame_index = 0;
    bool frame_started = false;
    uint16_t expected_frame_len = 0;

    while (1)
    {
        // 从队列接收字节
        if (xQueueReceive(handle->rx_queue, &rx_byte, portMAX_DELAY) == pdTRUE)
        {

            if (!frame_started)
            {
                // 查找帧头
                if (frame_index == 0 && rx_byte == 0xAA)
                {
                    frame_buffer[frame_index++] = rx_byte;
                }
                else if (frame_index == 1 && rx_byte == 0x55)
                {
                    frame_buffer[frame_index++] = rx_byte;
                    frame_started = true;
                }
                else
                {
                    frame_index = 0;
                    if (rx_byte == 0xAA)
                    {
                        frame_buffer[frame_index++] = rx_byte;
                    }
                }
            }
            else
            {
                // 接收帧数据
                frame_buffer[frame_index++] = rx_byte;

                // 获取数据长度
                if (frame_index == 4)
                {
                    uint8_t data_len = frame_buffer[COMM_OFFSET_DATA_LEN];
                    expected_frame_len = COMM_OFFSET_PAYLOAD + data_len + 1 + 2;
                }

                // 检查是否接收完整帧
                if (frame_index >= expected_frame_len)
                {
                    // 处理接收到的帧
                    comm_process_received_frame(handle, frame_buffer, frame_index);

                    // 重置状态
                    frame_index = 0;
                    frame_started = false;
                    expected_frame_len = 0;
                }

                // 防止缓冲区溢出
                if (frame_index >= COMM_MAX_FRAME_SIZE)
                {
                    frame_index = 0;
                    frame_started = false;
                    expected_frame_len = 0;
                    handle->stats.frame_errors++;
                }
            }
        }
    }
}

/**
 * @brief 发送任务
 */
static void comm_tx_task(void *argument)
{
    comm_handle_t *handle = (comm_handle_t *)argument;
    comm_command_t cmd;
    uint8_t frame_buffer[COMM_MAX_FRAME_SIZE];
    uint16_t frame_len;

    while (1)
    {
        // 从队列获取命令
        if (xQueueReceive(handle->cmd_queue, &cmd, portMAX_DELAY) == pdTRUE)
        {

            // 构建数据帧
            comm_error_t result = comm_build_frame(&cmd, frame_buffer, &frame_len);
            if (result == COMM_OK)
            {

                // 获取发送互斥量
                if (xSemaphoreTake(handle->tx_mutex, pdMS_TO_TICKS(COMM_MUTEX_TIMEOUT)) == pdTRUE)
                {

                    // 发送数据帧
                    if (HAL_UART_Transmit(handle->config.huart,
                                          frame_buffer,
                                          frame_len,
                                          cmd.timeout_ms) == HAL_OK)
                    {
                        handle->stats.tx_frames++;
                        handle->stats.tx_bytes += frame_len;

                        // 调用发送完成回调
                        if (handle->config.tx_callback)
                        {
                            handle->config.tx_callback(COMM_OK, handle->config.user_data);
                        }
                    }
                    else
                    {
                        handle->stats.timeout_errors++;

                        // 调用错误回调
                        if (handle->config.error_callback)
                        {
                            handle->config.error_callback(COMM_ERROR_TIMEOUT, handle->config.user_data);
                        }
                    }

                    xSemaphoreGive(handle->tx_mutex);
                }
            }
        }
    }
}

/**
 * @brief 心跳定时器回调
 */
static void comm_heartbeat_timer_callback(TimerHandle_t xTimer)
{
    comm_handle_t *handle = (comm_handle_t *)pvTimerGetTimerID(xTimer);
    if (handle && handle->initialized)
    {
        comm_send_heartbeat(handle);
    }
}

/**
 * @brief 超时定时器回调
 */
static void comm_timeout_timer_callback(TimerHandle_t xTimer)
{
    comm_handle_t *handle = (comm_handle_t *)pvTimerGetTimerID(xTimer);
    if (handle && handle->config.error_callback)
    {
        handle->config.error_callback(COMM_ERROR_TIMEOUT, handle->config.user_data);
    }
}

/**
 * @brief 处理接收到的帧
 */
static comm_error_t comm_process_received_frame(comm_handle_t *handle, const uint8_t *frame, uint16_t len)
{
    if (!comm_validate_frame(frame, len))
    {
        handle->stats.frame_errors++;
        return COMM_ERROR_FRAME;
    }

    // 更新统计信息
    handle->stats.rx_frames++;
    handle->stats.rx_bytes += len;

    // 提取帧信息
    comm_cmd_type_t cmd_type = frame[COMM_OFFSET_CMD_TYPE];
    uint8_t data_len = frame[COMM_OFFSET_DATA_LEN];
    const uint8_t *payload = &frame[COMM_OFFSET_PAYLOAD];

    // 处理特殊命令
    switch (cmd_type)
    {
    case COMM_CMD_PING:
        // 自动回复心跳
        comm_send_ack(handle, COMM_CMD_PING);
        break;

    case COMM_CMD_ACK:
    case COMM_CMD_NACK:
        // 停止超时定时器
        if (handle->timeout_timer)
        {
            xTimerStop(handle->timeout_timer, 0);
        }
        break;

    default:
        break;
    }

    // 调用注册的回调函数
    comm_call_rx_callback(handle, cmd_type, (uint8_t *)payload, data_len);

    return COMM_OK;
}

/**
 * @brief 构建数据帧
 */
static comm_error_t comm_build_frame(const comm_command_t *cmd, uint8_t *frame, uint16_t *frame_len)
{
    if (cmd == NULL || frame == NULL || frame_len == NULL)
    {
        return COMM_ERROR_PARAM;
    }

    if (cmd->data_len > COMM_MAX_PAYLOAD_SIZE)
    {
        return COMM_ERROR_PARAM;
    }

    uint16_t index = 0;

    // 帧头
    frame[index++] = (COMM_FRAME_HEADER >> 8) & 0xFF;
    frame[index++] = COMM_FRAME_HEADER & 0xFF;

    // 命令类型
    frame[index++] = cmd->cmd_type;

    // 数据长度
    frame[index++] = cmd->data_len;

    // 有效载荷
    if (cmd->data && cmd->data_len > 0)
    {
        memcpy(&frame[index], cmd->data, cmd->data_len);
        index += cmd->data_len;
    }

    // 校验码
    uint8_t checksum = comm_calculate_checksum(&frame[COMM_OFFSET_CMD_TYPE], cmd->data_len + 2);
    frame[index++] = checksum;

    // 帧尾
    frame[index++] = (COMM_FRAME_TAIL >> 8) & 0xFF;
    frame[index++] = COMM_FRAME_TAIL & 0xFF;

    *frame_len = index;

    return COMM_OK;
}

/**
 * @brief 改变模块状态
 */
static void comm_change_state(comm_handle_t *handle, comm_state_t new_state)
{
    if (handle == NULL)
    {
        return;
    }

    comm_state_t old_state = handle->state;
    handle->state = new_state;

    // 调用状态变化回调
    if (handle->config.state_callback && old_state != new_state)
    {
        handle->config.state_callback(old_state, new_state, handle->config.user_data);
    }
}

/**
 * @brief 调用接收回调函数
 */
static void comm_call_rx_callback(comm_handle_t *handle, comm_cmd_type_t cmd_type, uint8_t *data, uint16_t len)
{
    if (handle == NULL)
    {
        return;
    }

    // 查找并调用对应的回调函数
    for (uint8_t i = 0; i < handle->callback_count; i++)
    {
        if (handle->rx_callbacks[i].cmd_type == cmd_type)
        {
            handle->rx_callbacks[i].callback(cmd_type, data, len, handle->rx_callbacks[i].user_data);
            return;
        }
    }

    // 调用通用回调函数
    if (handle->config.rx_callback)
    {
        handle->config.rx_callback(cmd_type, data, len, handle->config.user_data);
    }
}

/**
 * @brief UART接收完成回调函数
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    // 这里需要根据具体的UART实例来处理
    // 由于可能有多个通信模块实例，需要通过某种方式找到对应的handle
    // 这里提供一个示例实现，实际使用时需要根据具体情况调整

    static comm_handle_t *g_comm_handle = NULL; // 全局句柄，需要在初始化时设置

    if (g_comm_handle && huart == g_comm_handle->config.huart)
    {
        BaseType_t xHigherPriorityTaskWoken = pdFALSE;

        // 将接收到的字节放入队列
        xQueueSendFromISR(g_comm_handle->rx_queue, &g_comm_handle->rx_buffer[0], &xHigherPriorityTaskWoken);

        // 继续接收下一个字节
        HAL_UART_Receive_IT(huart, &g_comm_handle->rx_buffer[0], 1);

        portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
    }
}