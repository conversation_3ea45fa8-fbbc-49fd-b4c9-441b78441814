/**
 ******************************************************************************
 * @file    comm_module.c
 * @brief   通信模块实现 - 用于接收和处理上位机命令
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "comm_module.h"
#include "led.h"

/* Private typedef -----------------------------------------------------------*/

/* Private define ------------------------------------------------------------*/
#define COMM_TASK_STACK_SIZE 512
#define COMM_TASK_PRIORITY osPriorityNormal

/* Private macro -------------------------------------------------------------*/

/* Private variables ---------------------------------------------------------*/
static uint8_t rx_buffer[COMM_RX_BUFFER_SIZE];
static uint8_t tx_buffer[COMM_TX_BUFFER_SIZE];
static uint16_t rx_index = 0;
static CommState_t comm_state = COMM_STATE_IDLE;
static CommStats_t comm_stats = {0};

/* FreeRTOS相关 */
static osThreadId_t commTaskHandle;
static const osThreadAttr_t commTask_attributes = {
    .name = "CommTask",
    .stack_size = COMM_TASK_STACK_SIZE,
    .priority = COMM_TASK_PRIORITY,
};

static osSemaphoreId_t rxSemaphoreHandle;
static const osSemaphoreAttr_t rxSemaphore_attributes = {
    .name = "RxSemaphore"};

static osMutexId_t commMutexHandle;
static const osMutexAttr_t commMutex_attributes = {
    .name = "CommMutex"};

/* Private function prototypes -----------------------------------------------*/
static void CommTask(void *argument);
static HAL_StatusTypeDef ProcessLedCommand(Command_t *command, Response_t *response);
static HAL_StatusTypeDef ProcessSystemCommand(Command_t *command, Response_t *response);
static HAL_StatusTypeDef ProcessTestCommand(Command_t *command, Response_t *response);
static void CreateResponse(Command_t *command, Response_t *response, uint8_t status, uint8_t *data, uint8_t data_len);

/* Exported functions --------------------------------------------------------*/

/**
 * @brief 初始化通信模块
 */
HAL_StatusTypeDef CommModule_Init(void)
{
    /* 初始化变量 */
    rx_index = 0;
    comm_state = COMM_STATE_IDLE;
    memset(&comm_stats, 0, sizeof(CommStats_t));
    memset(rx_buffer, 0, sizeof(rx_buffer));
    memset(tx_buffer, 0, sizeof(tx_buffer));

    /* 创建信号量 */
    rxSemaphoreHandle = osSemaphoreNew(1, 0, &rxSemaphore_attributes);
    if (rxSemaphoreHandle == NULL)
    {
        return HAL_ERROR;
    }

    /* 创建互斥量 */
    commMutexHandle = osMutexNew(&commMutex_attributes);
    if (commMutexHandle == NULL)
    {
        return HAL_ERROR;
    }

    /* 启动UART接收中断 */
    if (HAL_UART_Receive_IT(&huart1, &rx_buffer[rx_index], 1) != HAL_OK)
    {
        return HAL_ERROR;
    }

    return HAL_OK;
}

/**
 * @brief 启动通信任务
 */
void CommModule_StartTask(void)
{
    commTaskHandle = osThreadNew(CommTask, NULL, &commTask_attributes);
}

/**
 * @brief 通信任务
 */
static void CommTask(void *argument)
{
    Command_t command;
    // Response_t response;

    while (1)
    {
        /* 等待接收信号量 */
        if (osSemaphoreAcquire(rxSemaphoreHandle, osWaitForever) == osOK)
        {
            /* 获取互斥量 */
            if (osMutexAcquire(commMutexHandle, 100) == osOK)
            {
                comm_state = COMM_STATE_PROCESSING;

                /* 验证命令格式 */
                if (CommModule_ValidateCommand(rx_buffer, rx_index))
                {
                    /* 解析命令 */
                    memcpy(&command, rx_buffer, sizeof(Command_t));

                    /* 处理命令 */
                    if (CommModule_ProcessCommand(&command) == HAL_OK)
                    {
                        comm_stats.total_processed++;
                    }
                    else
                    {
                        comm_stats.total_errors++;
                    }
                }
                else
                {
                    comm_stats.format_errors++;
                    comm_stats.total_errors++;
                }

                /* 重置接收缓冲区 */
                rx_index = 0;
                memset(rx_buffer, 0, sizeof(rx_buffer));

                comm_state = COMM_STATE_IDLE;

                /* 重新启动UART接收 */
                HAL_UART_Receive_IT(&huart1, &rx_buffer[rx_index], 1);

                /* 释放互斥量 */
                osMutexRelease(commMutexHandle);
            }
        }
    }
}

/**
 * @brief 处理接收到的命令
 */
HAL_StatusTypeDef CommModule_ProcessCommand(Command_t *command)
{
    Response_t response;
    HAL_StatusTypeDef status = HAL_OK;

    /* 根据命令类型处理 */
    switch (command->cmd_type)
    {
    case CMD_TYPE_LED_CONTROL:
        status = ProcessLedCommand(command, &response);
        break;

    case CMD_TYPE_SYSTEM_INFO:
    case CMD_TYPE_STATUS:
        status = ProcessSystemCommand(command, &response);
        break;

    case CMD_TYPE_TEST:
        status = ProcessTestCommand(command, &response);
        break;

    case CMD_TYPE_RESET:
        /* 创建响应 */
        CreateResponse(command, &response, CMD_STATUS_SUCCESS, NULL, 0);
        CommModule_SendResponse(&response);
        /* 延时后复位 */
        osDelay(100);
        NVIC_SystemReset();
        break;

    default:
        CreateResponse(command, &response, CMD_STATUS_INVALID, NULL, 0);
        status = HAL_ERROR;
        break;
    }

    /* 发送响应 */
    if (status == HAL_OK && command->cmd_type != CMD_TYPE_RESET)
    {
        CommModule_SendResponse(&response);
    }

    return status;
}

/**
 * @brief 处理LED控制命令
 */
static HAL_StatusTypeDef ProcessLedCommand(Command_t *command, Response_t *response)
{
    uint8_t status = CMD_STATUS_SUCCESS;
    uint8_t response_data[4] = {0};

    switch (command->cmd_id)
    {
    case LED_CMD_ON:
        LED_On();
        response_data[0] = 0x01; // LED状态：开
        break;

    case LED_CMD_OFF:
        LED_Off();
        response_data[0] = 0x00; // LED状态：关
        break;

    case LED_CMD_TOGGLE:
        LED_Toggle();
        response_data[0] = 0x02; // LED状态：切换
        break;

    case LED_CMD_BLINK:
        /* 闪烁参数：次数(data[0]), 间隔ms(data[1]<<8|data[2]) */
        if (command->length >= 3)
        {
            uint8_t blink_count = command->data[0];
            uint16_t blink_interval = (command->data[1] << 8) | command->data[2];

            for (uint8_t i = 0; i < blink_count; i++)
            {
                LED_Toggle();
                osDelay(blink_interval);
                LED_Toggle();
                osDelay(blink_interval);
            }
            response_data[0] = 0x03; // LED状态：闪烁完成
        }
        else
        {
            status = CMD_STATUS_INVALID;
        }
        break;

    default:
        status = CMD_STATUS_INVALID;
        break;
    }

    CreateResponse(command, response, status, response_data, 1);
    return (status == CMD_STATUS_SUCCESS) ? HAL_OK : HAL_ERROR;
}

/**
 * @brief 处理系统命令
 */
static HAL_StatusTypeDef ProcessSystemCommand(Command_t *command, Response_t *response)
{
    uint8_t status = CMD_STATUS_SUCCESS;
    uint8_t response_data[32] = {0};
    uint8_t data_len = 0;

    switch (command->cmd_id)
    {
    case SYS_CMD_GET_INFO:
        /* 返回系统信息 */
        strcpy((char *)response_data, "STM32G474");
        data_len = strlen((char *)response_data);
        break;

    case SYS_CMD_GET_VERSION:
        /* 返回版本信息 */
        strcpy((char *)response_data, "V1.0.0");
        data_len = strlen((char *)response_data);
        break;

    case SYS_CMD_GET_STATUS:
        /* 返回系统状态 */
        response_data[0] = (comm_state & 0xFF);
        response_data[1] = (comm_stats.total_received >> 8) & 0xFF;
        response_data[2] = comm_stats.total_received & 0xFF;
        response_data[3] = (comm_stats.total_errors >> 8) & 0xFF;
        response_data[4] = comm_stats.total_errors & 0xFF;
        data_len = 5;
        break;

    default:
        status = CMD_STATUS_INVALID;
        break;
    }

    CreateResponse(command, response, status, response_data, data_len);
    return (status == CMD_STATUS_SUCCESS) ? HAL_OK : HAL_ERROR;
}

/**
 * @brief 处理测试命令
 */
static HAL_StatusTypeDef ProcessTestCommand(Command_t *command, Response_t *response)
{
    uint8_t status = CMD_STATUS_SUCCESS;

    /* 回显测试数据 */
    CreateResponse(command, response, status, command->data, command->length);

    return HAL_OK;
}

/**
 * @brief 创建响应
 */
static void CreateResponse(Command_t *command, Response_t *response, uint8_t status, uint8_t *data, uint8_t data_len)
{
    response->header = RESP_HEADER;
    response->length = data_len;
    response->cmd_type = command->cmd_type;
    response->cmd_id = command->cmd_id;
    response->status = status;

    /* 复制数据 */
    if (data != NULL && data_len > 0 && data_len <= COMM_MAX_DATA_LEN)
    {
        memcpy(response->data, data, data_len);
    }
    else
    {
        memset(response->data, 0, COMM_MAX_DATA_LEN);
    }

    /* 计算校验和 */
    uint8_t *resp_bytes = (uint8_t *)response;
    response->checksum = CommModule_CalculateChecksum(resp_bytes + 1, sizeof(Response_t) - 3);
    response->tail = RESP_TAIL;
}

/**
 * @brief 发送响应
 */
HAL_StatusTypeDef CommModule_SendResponse(Response_t *response)
{
    comm_state = COMM_STATE_SENDING;

    /* 复制响应到发送缓冲区 */
    memcpy(tx_buffer, response, sizeof(Response_t));

    /* 发送响应 */
    HAL_StatusTypeDef status = HAL_UART_Transmit(&huart1, tx_buffer, sizeof(Response_t), COMM_TIMEOUT_MS);

    comm_state = COMM_STATE_IDLE;

    return status;
}

/**
 * @brief 计算校验和
 */
uint8_t CommModule_CalculateChecksum(uint8_t *data, uint16_t length)
{
    uint8_t checksum = 0;
    for (uint16_t i = 0; i < length; i++)
    {
        checksum ^= data[i];
    }
    return checksum;
}

/**
 * @brief 验证命令格式
 */
uint8_t CommModule_ValidateCommand(uint8_t *buffer, uint16_t length)
{
    if (length < sizeof(Command_t))
    {
        return 0;
    }

    Command_t *cmd = (Command_t *)buffer;

    /* 检查命令头和尾 */
    if (cmd->header != COMM_HEADER || cmd->tail != COMM_TAIL)
    {
        return 0;
    }

    /* 检查数据长度 */
    if (cmd->length > COMM_MAX_DATA_LEN)
    {
        return 0;
    }

    /* 验证校验和 */
    uint8_t calculated_checksum = CommModule_CalculateChecksum(buffer + 1, sizeof(Command_t) - 3);
    if (calculated_checksum != cmd->checksum)
    {
        comm_stats.checksum_errors++;
        return 0;
    }

    return 1;
}

/**
 * @brief UART接收完成回调函数
 */
void CommModule_UartRxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART1)
    {
        rx_index++;

        /* 检查是否接收到完整命令 */
        if (rx_index >= sizeof(Command_t))
        {
            /* 检查命令头 */
            if (rx_buffer[0] == COMM_HEADER && rx_buffer[sizeof(Command_t) - 1] == COMM_TAIL)
            {
                comm_stats.total_received++;
                /* 释放信号量，通知任务处理命令 */
                osSemaphoreRelease(rxSemaphoreHandle);
                return;
            }
            else
            {
                /* 重置接收缓冲区 */
                rx_index = 0;
                memset(rx_buffer, 0, sizeof(rx_buffer));
            }
        }

        /* 防止缓冲区溢出 */
        if (rx_index >= COMM_RX_BUFFER_SIZE)
        {
            rx_index = 0;
            memset(rx_buffer, 0, sizeof(rx_buffer));
        }

        /* 继续接收下一个字节 */
        HAL_UART_Receive_IT(&huart1, &rx_buffer[rx_index], 1);
    }
}

/**
 * @brief UART发送完成回调函数
 */
void CommModule_UartTxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART1)
    {
        comm_state = COMM_STATE_IDLE;
    }
}

/**
 * @brief 获取通信统计信息
 */
CommStats_t *CommModule_GetStats(void)
{
    return &comm_stats;
}

/**
 * @brief 重置通信统计信息
 */
void CommModule_ResetStats(void)
{
    memset(&comm_stats, 0, sizeof(CommStats_t));
}
