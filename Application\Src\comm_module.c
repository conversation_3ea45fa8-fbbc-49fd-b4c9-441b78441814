/**
 ******************************************************************************
 * @file    comm_module.c
 * @brief   串口通信模块源文件
 * <AUTHOR>
 * @date    2025-08-04
 * @version 1.0.0
 ******************************************************************************
 * @attention
 *
 * 本模块实现完整的串口通信功能，包括：
 * - 基于FreeRTOS的多任务处理
 * - 完整的协议栈实现
 * - 错误处理和重试机制
 * - 统计信息收集
 *
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "comm_module.h"
#include "stm32g4xx_hal.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

/* Private defines -----------------------------------------------------------*/
#define COMM_TASK_STACK_SIZE 512                  // 任务栈大小
#define COMM_TASK_PRIORITY (tskIDLE_PRIORITY + 1) // 任务优先级
#define COMM_QUEUE_TIMEOUT 100                    // 队列超时时间
#define COMM_MUTEX_TIMEOUT 1000                   // 互斥锁超时时间

/* Private variables ---------------------------------------------------------*/
static const char *error_strings[] = {
    "Success",                // 成功
    "General error",          // 一般错误
    "Parameter error",        // 参数错误
    "Timeout error",          // 超时错误
    "Checksum error",         // 校验和错误
    "Frame format error",     // 帧格式错误
    "Buffer full",            // 缓冲区满
    "Not initialized",        // 未初始化
    "Busy",                   // 忙
    "No memory",              // 内存不足
    "Unsupported operation"}; // 不支持的操作

/* Private function prototypes -----------------------------------------------*/
static void commRxTask(void *argument);
static void commTxTask(void *argument);
static void commTimeoutCallback(void *argument);
static comm_error_t buildFrame(uint8_t *frame, comm_cmd_type_t cmd_type,
                               const uint8_t *data, uint8_t len, uint16_t *frame_len);
static comm_error_t parseFrame(const uint8_t *frame, uint16_t frame_len,
                               comm_message_t *message);
static comm_error_t comm_process_received_frame(comm_handle_t *handle,
                                                const uint8_t *frame, uint16_t len);
static comm_error_t handleBuiltinCommands(comm_handle_t *handle,
                                          const comm_message_t *message,
                                          uint8_t *response_data,
                                          uint8_t *response_len);

/* Public functions ----------------------------------------------------------*/

/**
 * @brief 初始化通信模块
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 通信模块句柄
 * @param huart UART句柄
 * @param config 串口配置参数
 * @return 错误码
 * @note 必须在使用其他功能前调用此函数
 */
comm_error_t comm_init(comm_handle_t *handle,
                       UART_HandleTypeDef *huart,
                       const comm_uart_config_t *config)
{
    if (!handle || !huart)
    {
        return COMM_ERROR_PARAM;
    }

    // 检查是否已初始化
    if (handle->initialized)
    {
        return COMM_ERROR;
    }

    // 清零句柄
    memset(handle, 0, sizeof(comm_handle_t));

    // 保存UART句柄
    handle->huart = huart;

    // 复制配置参数
    if (config)
    {
        handle->config = *config;
    }
    else
    {
        // 使用默认配置
        handle->config.baudrate = 115200;
        handle->config.word_length = UART_WORDLENGTH_8B;
        handle->config.stop_bits = UART_STOPBITS_1;
        handle->config.parity = UART_PARITY_NONE;
        handle->config.flow_control = UART_HWCONTROL_NONE;
        handle->config.timeout = COMM_DEFAULT_TIMEOUT;
    }

    // 创建FreeRTOS对象
    const osMessageQueueAttr_t queue_attr = {
        .name = "CommQueue"};

    handle->rx_queue = osMessageQueueNew(COMM_QUEUE_SIZE, sizeof(comm_message_t), &queue_attr);
    handle->tx_queue = osMessageQueueNew(COMM_QUEUE_SIZE, sizeof(comm_message_t), &queue_attr);

    if (!handle->rx_queue || !handle->tx_queue)
    {
        return COMM_ERROR_NO_MEMORY;
    }

    const osMutexAttr_t mutex_attr = {
        .name = "CommMutex"};

    handle->mutex = osMutexNew(&mutex_attr);
    if (!handle->mutex)
    {
        return COMM_ERROR_NO_MEMORY;
    }

    const osTimerAttr_t timer_attr = {
        .name = "CommTimer"};

    handle->timeout_timer = osTimerNew(commTimeoutCallback, osTimerOnce, handle, &timer_attr);
    if (!handle->timeout_timer)
    {
        return COMM_ERROR_NO_MEMORY;
    }

    // 初始化状态
    handle->state = COMM_STATE_READY;
    handle->initialized = true;

    return COMM_OK;
}

/**
 * @brief 反初始化通信模块
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 通信模块句柄
 * @return 错误码
 */
comm_error_t comm_deinit(comm_handle_t *handle)
{
    if (!handle || !handle->initialized)
    {
        return COMM_ERROR_NOT_INIT;
    }

    // 停止模块
    comm_stop(handle);

    // 删除FreeRTOS对象
    if (handle->rx_queue)
    {
        osMessageQueueDelete(handle->rx_queue);
        handle->rx_queue = NULL;
    }

    if (handle->tx_queue)
    {
        osMessageQueueDelete(handle->tx_queue);
        handle->tx_queue = NULL;
    }

    if (handle->mutex)
    {
        osMutexDelete(handle->mutex);
        handle->mutex = NULL;
    }

    if (handle->timeout_timer)
    {
        osTimerDelete(handle->timeout_timer);
        handle->timeout_timer = NULL;
    }

    // 清零句柄
    memset(handle, 0, sizeof(comm_handle_t));

    return COMM_OK;
}

/**
 * @brief 启动通信模块
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 通信模块句柄
 * @return 错误码
 * @note 启动接收和发送任务
 */
comm_error_t comm_start(comm_handle_t *handle)
{
    if (!handle || !handle->initialized)
    {
        return COMM_ERROR_NOT_INIT;
    }

    if (handle->state != COMM_STATE_READY)
    {
        return COMM_ERROR_BUSY;
    }

    // 创建接收任务
    const osThreadAttr_t rx_task_attr = {
        .name = "CommRxTask",
        .stack_size = COMM_TASK_STACK_SIZE,
        .priority = (osPriority_t)COMM_TASK_PRIORITY};

    handle->rx_task_handle = osThreadNew(commRxTask, handle, &rx_task_attr);
    if (!handle->rx_task_handle)
    {
        return COMM_ERROR_NO_MEMORY;
    }

    // 创建发送任务
    const osThreadAttr_t tx_task_attr = {
        .name = "CommTxTask",
        .stack_size = COMM_TASK_STACK_SIZE,
        .priority = (osPriority_t)COMM_TASK_PRIORITY};

    handle->tx_task_handle = osThreadNew(commTxTask, handle, &tx_task_attr);
    if (!handle->tx_task_handle)
    {
        osThreadTerminate(handle->rx_task_handle);
        handle->rx_task_handle = NULL;
        return COMM_ERROR_NO_MEMORY;
    }

    // 启动UART接收中断
    HAL_UART_Receive_IT(handle->huart, &handle->rx_buffer[handle->rx_index], 1);

    handle->state = COMM_STATE_READY;
    return COMM_OK;
}

/**
 * @brief 停止通信模块
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 通信模块句柄
 * @return 错误码
 */
comm_error_t comm_stop(comm_handle_t *handle)
{
    if (!handle || !handle->initialized)
    {
        return COMM_ERROR_NOT_INIT;
    }

    // 停止UART接收
    HAL_UART_AbortReceive_IT(handle->huart);

    // 终止任务
    if (handle->rx_task_handle)
    {
        osThreadTerminate(handle->rx_task_handle);
        handle->rx_task_handle = NULL;
    }

    if (handle->tx_task_handle)
    {
        osThreadTerminate(handle->tx_task_handle);
        handle->tx_task_handle = NULL;
    }

    // 停止定时器
    if (handle->timeout_timer)
    {
        osTimerStop(handle->timeout_timer);
    }

    handle->state = COMM_STATE_READY;
    return COMM_OK;
}

/**
 * @brief 发送数据
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 通信模块句柄
 * @param cmd_type 命令类型
 * @param data 数据指针
 * @param len 数据长度
 * @param timeout 超时时间(ms)
 * @return 错误码
 */
comm_error_t comm_send_data(comm_handle_t *handle,
                            comm_cmd_type_t cmd_type,
                            const uint8_t *data,
                            uint8_t len,
                            uint32_t timeout)
{
    if (!handle || !handle->initialized)
    {
        return COMM_ERROR_NOT_INIT;
    }

    if (len > COMM_MAX_PAYLOAD_SIZE)
    {
        return COMM_ERROR_PARAM;
    }

    // 创建消息
    comm_message_t message;
    message.cmd_type = cmd_type;
    message.data_len = len;
    message.timestamp = osKernelGetTickCount();

    if (data && len > 0)
    {
        memcpy(message.data, data, len);
    }

    // 发送到发送队列
    osStatus_t status = osMessageQueuePut(handle->tx_queue, &message, 0, timeout);
    if (status != osOK)
    {
        return COMM_ERROR_TIMEOUT;
    }

    return COMM_OK;
}

/**
 * @brief 发送应答
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 通信模块句柄
 * @param cmd_type 原命令类型
 * @return 错误码
 */
comm_error_t comm_send_ack(comm_handle_t *handle, comm_cmd_type_t cmd_type)
{
    uint8_t ack_data = cmd_type;
    return comm_send_data(handle, COMM_CMD_ACK, &ack_data, 1, COMM_ACK_TIMEOUT);
}

/**
 * @brief 发送否定应答
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 通信模块句柄
 * @param cmd_type 原命令类型
 * @param error_code 错误码
 * @return 错误码
 */
comm_error_t comm_send_nack(comm_handle_t *handle,
                            comm_cmd_type_t cmd_type,
                            comm_error_t error_code)
{
    uint8_t nack_data[2] = {cmd_type, error_code};
    return comm_send_data(handle, COMM_CMD_NACK, nack_data, 2, COMM_ACK_TIMEOUT);
}

/**
 * @brief 注册命令回调函数
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 通信模块句柄
 * @param callback 回调函数指针
 * @return 错误码
 */
comm_error_t comm_register_callback(comm_handle_t *handle,
                                    comm_cmd_callback_t callback)
{
    if (!handle || !handle->initialized)
    {
        return COMM_ERROR_NOT_INIT;
    }

    handle->cmd_callback = callback;
    return COMM_OK;
}

/**
 * @brief 获取模块状态
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 通信模块句柄
 * @return 模块状态
 */
comm_state_t comm_get_state(const comm_handle_t *handle)
{
    if (!handle || !handle->initialized)
    {
        return COMM_STATE_UNINITIALIZED;
    }

    return handle->state;
}

/**
 * @brief 获取统计信息
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 通信模块句柄
 * @param stats 统计信息指针
 * @return 错误码
 */
comm_error_t comm_get_statistics(const comm_handle_t *handle,
                                 comm_statistics_t *stats)
{
    if (!handle || !handle->initialized || !stats)
    {
        return COMM_ERROR_PARAM;
    }

    *stats = handle->stats;
    return COMM_OK;
}

/**
 * @brief 清除统计信息
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 通信模块句柄
 * @return 错误码
 */
comm_error_t comm_clear_statistics(comm_handle_t *handle)
{
    if (!handle || !handle->initialized)
    {
        return COMM_ERROR_NOT_INIT;
    }

    memset(&handle->stats, 0, sizeof(comm_statistics_t));
    return COMM_OK;
}

/**
 * @brief 计算校验码
 * @param data 数据指针
 * @param len 数据长度
 * @return 校验码
 */
uint8_t comm_calculate_checksum(const uint8_t *data, uint16_t len)
{
    uint8_t checksum = 0;
    for (uint16_t i = 0; i < len; i++)
    {
        checksum += data[i];
    }
    return checksum;
}

/**
 * @brief 验证帧格式
 * @param frame 帧数据指针
 * @param len 帧长度
 * @return true:有效帧 false:无效帧
 */
bool comm_validate_frame(const uint8_t *frame, uint16_t len)
{
    if (!frame || len < COMM_MIN_FRAME_SIZE)
    {
        return false;
    }

    // 检查帧头
    uint16_t header = (frame[COMM_OFFSET_HEADER] << 8) | frame[COMM_OFFSET_HEADER + 1];
    if (header != COMM_FRAME_HEADER)
    {
        return false;
    }

    // 检查数据长度
    uint8_t data_len = frame[COMM_OFFSET_DATA_LEN];
    if (len != COMM_MIN_FRAME_SIZE + data_len)
    {
        return false;
    }

    // 检查帧尾
    uint16_t tail_offset = COMM_OFFSET_PAYLOAD + data_len + 1;
    if (tail_offset + 1 >= len)
    {
        return false;
    }

    uint16_t tail = (frame[tail_offset] << 8) | frame[tail_offset + 1];
    if (tail != COMM_FRAME_TAIL)
    {
        return false;
    }

    // 检查校验码
    uint8_t calculated_checksum = comm_calculate_checksum(&frame[COMM_OFFSET_CMD_TYPE], data_len + 2);
    uint8_t received_checksum = frame[COMM_OFFSET_PAYLOAD + data_len];
    if (calculated_checksum != received_checksum)
    {
        return false;
    }

    return true;
}

/**
 * @brief 获取错误描述字符串
 */
const char *comm_get_error_string(comm_error_t error)
{
    if (error >= sizeof(error_strings) / sizeof(error_strings[0]))
    {
        return "Unknown error";
    }

    return error_strings[error];
}

/**
 * @brief 获取模块版本信息
 */
void comm_get_version(uint8_t *major, uint8_t *minor, uint8_t *patch)
{
    if (major)
        *major = COMM_MODULE_VERSION_MAJOR;
    if (minor)
        *minor = COMM_MODULE_VERSION_MINOR;
    if (patch)
        *patch = COMM_MODULE_VERSION_PATCH;
}

/* Private functions ---------------------------------------------------------*/

/**
 * @brief 接收任务
 * @param argument 任务参数(通信模块句柄)
 */
static void commRxTask(void *argument)
{
    comm_handle_t *handle = (comm_handle_t *)argument;
    uint8_t frame_buffer[COMM_MAX_FRAME_SIZE];

    while (1)
    {
        // 等待接收数据
        osDelay(1);

        // 检查是否有完整帧
        if (handle->rx_index >= COMM_MIN_FRAME_SIZE)
        {
            // 查找帧头
            for (uint16_t i = 0; i <= handle->rx_index - 2; i++)
            {
                uint16_t header = (handle->rx_buffer[i] << 8) | handle->rx_buffer[i + 1];
                if (header == COMM_FRAME_HEADER)
                {
                    // 找到帧头，检查是否有完整帧
                    if (i + COMM_MIN_FRAME_SIZE <= handle->rx_index)
                    {
                        uint8_t data_len = handle->rx_buffer[i + COMM_OFFSET_DATA_LEN];
                        uint16_t frame_len = COMM_MIN_FRAME_SIZE + data_len;

                        if (i + frame_len <= handle->rx_index)
                        {
                            // 复制完整帧
                            memcpy(frame_buffer, &handle->rx_buffer[i], frame_len);

                            // 处理帧
                            comm_process_received_frame(handle, frame_buffer, frame_len);

                            // 移除已处理的数据
                            uint16_t remaining = handle->rx_index - (i + frame_len);
                            if (remaining > 0)
                            {
                                memmove(handle->rx_buffer, &handle->rx_buffer[i + frame_len], remaining);
                            }
                            handle->rx_index = remaining;
                            break;
                        }
                    }
                }
            }
        }
    }
}

/**
 * @brief 发送任务
 * @param argument 任务参数(通信模块句柄)
 */
static void commTxTask(void *argument)
{
    comm_handle_t *handle = (comm_handle_t *)argument;
    comm_message_t message;
    uint8_t frame_buffer[COMM_MAX_FRAME_SIZE];
    uint16_t frame_len;

    while (1)
    {
        // 等待发送消息
        osStatus_t status = osMessageQueueGet(handle->tx_queue, &message, NULL, osWaitForever);
        if (status == osOK)
        {
            // 构建帧
            if (buildFrame(frame_buffer, message.cmd_type, message.data, message.data_len, &frame_len) == COMM_OK)
            {
                // 发送帧
                if (osMutexAcquire(handle->mutex, COMM_MUTEX_TIMEOUT) == osOK)
                {
                    handle->transmitting = true;
                    HAL_StatusTypeDef hal_status = HAL_UART_Transmit(handle->huart, frame_buffer, frame_len, handle->config.timeout);

                    if (hal_status == HAL_OK)
                    {
                        handle->stats.tx_frames++;
                        handle->stats.tx_bytes += frame_len;
                    }
                    else
                    {
                        handle->stats.timeout_errors++;
                    }

                    handle->transmitting = false;
                    osMutexRelease(handle->mutex);
                }
            }
        }
    }
}

/**
 * @brief 超时回调函数
 * @param argument 回调参数(通信模块句柄)
 */
static void commTimeoutCallback(void *argument)
{
    comm_handle_t *handle = (comm_handle_t *)argument;
    if (handle)
    {
        handle->stats.timeout_errors++;
    }
}

/**
 * @brief 构建数据帧
 * @param frame 帧缓冲区
 * @param cmd_type 命令类型
 * @param data 数据指针
 * @param len 数据长度
 * @param frame_len 帧长度指针
 * @return 错误码
 */
static comm_error_t buildFrame(uint8_t *frame, comm_cmd_type_t cmd_type,
                               const uint8_t *data, uint8_t len, uint16_t *frame_len)
{
    if (!frame || !frame_len)
    {
        return COMM_ERROR_PARAM;
    }

    if (len > COMM_MAX_PAYLOAD_SIZE)
    {
        return COMM_ERROR_PARAM;
    }

    uint16_t index = 0;

    // 帧头
    frame[index++] = (COMM_FRAME_HEADER >> 8) & 0xFF;
    frame[index++] = COMM_FRAME_HEADER & 0xFF;

    // 命令类型
    frame[index++] = cmd_type;

    // 数据长度
    frame[index++] = len;

    // 有效载荷
    if (data && len > 0)
    {
        memcpy(&frame[index], data, len);
        index += len;
    }

    // 校验码
    uint8_t checksum = comm_calculate_checksum(&frame[COMM_OFFSET_CMD_TYPE], len + 2);
    frame[index++] = checksum;

    // 帧尾
    frame[index++] = (COMM_FRAME_TAIL >> 8) & 0xFF;
    frame[index++] = COMM_FRAME_TAIL & 0xFF;

    *frame_len = index;
    return COMM_OK;
}

/**
 * @brief 解析数据帧
 * @param frame 帧数据指针
 * @param frame_len 帧长度
 * @param message 消息指针
 * @return 错误码
 */
static comm_error_t parseFrame(const uint8_t *frame, uint16_t frame_len,
                               comm_message_t *message)
{
    if (!frame || !message || frame_len < COMM_MIN_FRAME_SIZE)
    {
        return COMM_ERROR_PARAM;
    }

    // 验证帧格式
    if (!comm_validate_frame(frame, frame_len))
    {
        return COMM_ERROR_FRAME;
    }

    // 提取消息信息
    message->cmd_type = (comm_cmd_type_t)frame[COMM_OFFSET_CMD_TYPE];
    message->data_len = frame[COMM_OFFSET_DATA_LEN];
    message->timestamp = osKernelGetTickCount();

    // 复制数据
    if (message->data_len > 0)
    {
        memcpy(message->data, &frame[COMM_OFFSET_PAYLOAD], message->data_len);
    }

    return COMM_OK;
}

/**
 * @brief 处理接收到的帧
 */
static comm_error_t comm_process_received_frame(comm_handle_t *handle, const uint8_t *frame, uint16_t len)
{
    if (!comm_validate_frame(frame, len))
    {
        handle->stats.frame_errors++;
        return COMM_ERROR_FRAME;
    }

    // 更新统计信息
    handle->stats.rx_frames++;
    handle->stats.rx_bytes += len;

    // 提取帧信息
    comm_cmd_type_t cmd_type = (comm_cmd_type_t)frame[COMM_OFFSET_CMD_TYPE];

    // 处理特殊命令
    switch (cmd_type)
    {
    case COMM_CMD_PING:
        // 自动回复心跳
        comm_send_ack(handle, COMM_CMD_PING);
        break;

    case COMM_CMD_ACK:
    case COMM_CMD_NACK:
        // 停止超时定时器
        if (handle->timeout_timer)
        {
            osTimerStop(handle->timeout_timer);
        }
        break;

    default:
        break;
    }

    // 解析消息
    comm_message_t message;
    if (parseFrame(frame, len, &message) == COMM_OK)
    {
        // 处理内置命令
        uint8_t response_data[COMM_MAX_PAYLOAD_SIZE];
        uint8_t response_len = 0;

        comm_error_t result = handleBuiltinCommands(handle, &message, response_data, &response_len);

        if (result == COMM_ERROR_UNSUPPORTED)
        {
            // 调用用户回调函数
            if (handle->cmd_callback)
            {
                result = handle->cmd_callback(message.cmd_type, message.data, message.data_len,
                                              response_data, &response_len);
            }
        }

        // 发送响应
        if (result == COMM_OK && response_len > 0)
        {
            comm_send_data(handle, message.cmd_type, response_data, response_len, COMM_DEFAULT_TIMEOUT);
        }
        else if (result != COMM_OK)
        {
            comm_send_nack(handle, message.cmd_type, result);
        }

        // 将消息放入接收队列
        osMessageQueuePut(handle->rx_queue, &message, 0, 0);
    }

    return COMM_OK;
}

/**
 * @brief 处理内置命令
 * @param handle 通信模块句柄
 * @param message 消息指针
 * @param response_data 响应数据指针
 * @param response_len 响应数据长度指针
 * @return 错误码
 */
static comm_error_t handleBuiltinCommands(comm_handle_t *handle,
                                          const comm_message_t *message,
                                          uint8_t *response_data,
                                          uint8_t *response_len)
{
    switch (message->cmd_type)
    {
    case COMM_CMD_GET_VERSION:
        response_data[0] = COMM_MODULE_VERSION_MAJOR;
        response_data[1] = COMM_MODULE_VERSION_MINOR;
        response_data[2] = COMM_MODULE_VERSION_PATCH;
        *response_len = 3;
        return COMM_OK;

    case COMM_CMD_GET_STATUS:
        response_data[0] = handle->state;
        response_data[1] = handle->initialized ? 1 : 0;
        response_data[2] = handle->receiving ? 1 : 0;
        response_data[3] = handle->transmitting ? 1 : 0;
        *response_len = 4;
        return COMM_OK;

    case COMM_CMD_RESET:
        // 软件复位
        NVIC_SystemReset();
        return COMM_OK;

    default:
        return COMM_ERROR_UNSUPPORTED;
    }
}

/**
 * @brief UART接收完成回调函数
 * @param huart UART句柄
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    // 这里需要根据实际的UART句柄来处理
    // 由于我们不知道具体的全局句柄变量名，这里提供一个示例实现
    // 实际使用时需要根据项目中的全局变量进行调整

    // 示例：假设有一个全局的通信句柄
    // extern comm_handle_t g_comm_handle;
    //
    // if (huart == g_comm_handle.huart)
    // {
    //     g_comm_handle.rx_index++;
    //     if (g_comm_handle.rx_index < COMM_RX_BUFFER_SIZE)
    //     {
    //         HAL_UART_Receive_IT(huart, &g_comm_handle.rx_buffer[g_comm_handle.rx_index], 1);
    //     }
    //     else
    //     {
    //         g_comm_handle.rx_index = 0;
    //         HAL_UART_Receive_IT(huart, &g_comm_handle.rx_buffer[0], 1);
    //     }
    // }
}

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
