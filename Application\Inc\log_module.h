/**
 ******************************************************************************
 * @file    log_module.h
 * @brief   日志处理模块头文件
 * <AUTHOR>
 * @date    2025-08-04
 * @version 1.0.0
 ******************************************************************************
 * @attention
 * 
 * 本模块提供完整的日志处理功能，包括：
 * - 多级日志输出（DEBUG、INFO、WARN、ERROR）
 * - 时间戳和日志级别标识
 * - 线程安全的日志输出
 * - 日志开关和级别过滤功能
 * - 日志统计和缓冲区管理
 * 
 * 使用前请确保：
 * 1. 已正确初始化USART2
 * 2. 已启用FreeRTOS
 * 3. 已实现printf重定向
 * 
 ******************************************************************************
 */

#ifndef __LOG_MODULE_H
#define __LOG_MODULE_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32g4xx_hal.h"
#include "cmsis_os.h"
#include <stdint.h>
#include <stdbool.h>
#include <stdarg.h>
#include <stdio.h>

/* 模块版本信息 ---------------------------------------------------------------*/
#define LOG_MODULE_VERSION_MAJOR    1
#define LOG_MODULE_VERSION_MINOR    0
#define LOG_MODULE_VERSION_PATCH    0

/* 日志配置常量 ---------------------------------------------------------------*/
#define LOG_MAX_MESSAGE_LENGTH      256         /**< 最大日志消息长度 */
#define LOG_BUFFER_SIZE             1024        /**< 日志缓冲区大小 */
#define LOG_QUEUE_SIZE              20          /**< 日志消息队列大小 */
#define LOG_TASK_STACK_SIZE         512         /**< 日志任务栈大小 */
#define LOG_TASK_PRIORITY           (tskIDLE_PRIORITY + 2)  /**< 日志任务优先级 */

/* 日志格式配置 ---------------------------------------------------------------*/
#define LOG_ENABLE_TIMESTAMP        1           /**< 启用时间戳 */
#define LOG_ENABLE_LEVEL_TAG        1           /**< 启用级别标签 */
#define LOG_ENABLE_FILE_LINE        1           /**< 启用文件名和行号 */
#define LOG_ENABLE_FUNCTION_NAME    1           /**< 启用函数名 */
#define LOG_ENABLE_COLOR            0           /**< 启用颜色输出（终端支持） */

/* 日志颜色定义（ANSI转义序列） -----------------------------------------------*/
#if LOG_ENABLE_COLOR
#define LOG_COLOR_RED       "\033[31m"
#define LOG_COLOR_YELLOW    "\033[33m"
#define LOG_COLOR_GREEN     "\033[32m"
#define LOG_COLOR_BLUE      "\033[34m"
#define LOG_COLOR_RESET     "\033[0m"
#else
#define LOG_COLOR_RED       ""
#define LOG_COLOR_YELLOW    ""
#define LOG_COLOR_GREEN     ""
#define LOG_COLOR_BLUE      ""
#define LOG_COLOR_RESET     ""
#endif

/**
 * @brief 日志级别枚举
 */
typedef enum {
    LOG_LEVEL_DEBUG = 0,        /**< 调试级别 - 详细的调试信息 */
    LOG_LEVEL_INFO,             /**< 信息级别 - 一般信息 */
    LOG_LEVEL_WARN,             /**< 警告级别 - 警告信息 */
    LOG_LEVEL_ERROR,            /**< 错误级别 - 错误信息 */
    LOG_LEVEL_NONE              /**< 无日志输出 */
} log_level_t;

/**
 * @brief 日志错误码枚举
 */
typedef enum {
    LOG_OK                  = 0x00,     /**< 成功 */
    LOG_ERROR               = 0x01,     /**< 一般错误 */
    LOG_ERROR_PARAM         = 0x02,     /**< 参数错误 */
    LOG_ERROR_NOT_INIT      = 0x03,     /**< 未初始化 */
    LOG_ERROR_BUFFER_FULL   = 0x04,     /**< 缓冲区满 */
    LOG_ERROR_NO_MEMORY     = 0x05,     /**< 内存不足 */
    LOG_ERROR_TIMEOUT       = 0x06      /**< 超时错误 */
} log_error_t;

/**
 * @brief 日志状态枚举
 */
typedef enum {
    LOG_STATE_UNINITIALIZED = 0,        /**< 未初始化 */
    LOG_STATE_READY,                    /**< 就绪状态 */
    LOG_STATE_RUNNING,                  /**< 运行状态 */
    LOG_STATE_ERROR                     /**< 错误状态 */
} log_state_t;

/**
 * @brief 日志消息结构体
 */
typedef struct {
    log_level_t level;                  /**< 日志级别 */
    uint32_t timestamp;                 /**< 时间戳 */
    char message[LOG_MAX_MESSAGE_LENGTH]; /**< 日志消息内容 */
    const char *file;                   /**< 源文件名 */
    const char *function;               /**< 函数名 */
    uint32_t line;                      /**< 行号 */
} log_message_t;

/**
 * @brief 日志统计信息结构体
 */
typedef struct {
    uint32_t total_messages;            /**< 总消息数 */
    uint32_t debug_count;               /**< DEBUG级别消息数 */
    uint32_t info_count;                /**< INFO级别消息数 */
    uint32_t warn_count;                /**< WARN级别消息数 */
    uint32_t error_count;               /**< ERROR级别消息数 */
    uint32_t dropped_messages;          /**< 丢弃的消息数 */
    uint32_t buffer_overflows;          /**< 缓冲区溢出次数 */
} log_statistics_t;

/**
 * @brief 日志配置结构体
 */
typedef struct {
    log_level_t min_level;              /**< 最小输出级别 */
    bool enable_timestamp;              /**< 启用时间戳 */
    bool enable_level_tag;              /**< 启用级别标签 */
    bool enable_file_line;              /**< 启用文件名和行号 */
    bool enable_function_name;          /**< 启用函数名 */
    bool enable_color;                  /**< 启用颜色输出 */
    uint32_t buffer_size;               /**< 缓冲区大小 */
    uint32_t queue_size;                /**< 队列大小 */
} log_config_t;

/**
 * @brief 日志模块句柄结构体
 */
typedef struct {
    log_config_t config;                /**< 日志配置 */
    log_state_t state;                  /**< 模块状态 */
    log_statistics_t stats;             /**< 统计信息 */
    
    /* FreeRTOS对象 */
    osThreadId_t log_task_handle;       /**< 日志任务句柄 */
    osMessageQueueId_t log_queue;       /**< 日志消息队列 */
    osMutexId_t log_mutex;              /**< 日志互斥锁 */
    
    /* 缓冲区 */
    char *log_buffer;                   /**< 日志缓冲区 */
    uint32_t buffer_index;              /**< 缓冲区索引 */
    
    /* 状态标志 */
    bool initialized;                   /**< 初始化标志 */
    bool enabled;                       /**< 日志使能标志 */
} log_handle_t;

/* 全局函数声明 ---------------------------------------------------------------*/

/**
 * @brief 初始化日志模块
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 日志模块句柄
 * @param config 日志配置参数（可为NULL使用默认配置）
 * @return 错误码
 * @note 必须在使用其他功能前调用此函数
 */
log_error_t log_init(log_handle_t *handle, const log_config_t *config);

/**
 * @brief 反初始化日志模块
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 日志模块句柄
 * @return 错误码
 */
log_error_t log_deinit(log_handle_t *handle);

/**
 * @brief 启动日志模块
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 日志模块句柄
 * @return 错误码
 * @note 启动日志处理任务
 */
log_error_t log_start(log_handle_t *handle);

/**
 * @brief 停止日志模块
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 日志模块句柄
 * @return 错误码
 */
log_error_t log_stop(log_handle_t *handle);

/**
 * @brief 输出日志消息
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 日志模块句柄
 * @param level 日志级别
 * @param file 源文件名
 * @param function 函数名
 * @param line 行号
 * @param format 格式化字符串
 * @param ... 可变参数
 * @return 错误码
 */
log_error_t log_output(log_handle_t *handle, log_level_t level, 
                      const char *file, const char *function, uint32_t line,
                      const char *format, ...);

/**
 * @brief 设置日志级别
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 日志模块句柄
 * @param level 最小输出级别
 * @return 错误码
 */
log_error_t log_set_level(log_handle_t *handle, log_level_t level);

/**
 * @brief 获取日志级别
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 日志模块句柄
 * @return 当前日志级别
 */
log_level_t log_get_level(const log_handle_t *handle);

/**
 * @brief 启用/禁用日志输出
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 日志模块句柄
 * @param enable true:启用 false:禁用
 * @return 错误码
 */
log_error_t log_enable(log_handle_t *handle, bool enable);

/**
 * @brief 获取日志统计信息
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 日志模块句柄
 * @param stats 统计信息指针
 * @return 错误码
 */
log_error_t log_get_statistics(const log_handle_t *handle, log_statistics_t *stats);

/**
 * @brief 清除日志统计信息
 * <AUTHOR>
 * @date 2025-08-04
 * @param handle 日志模块句柄
 * @return 错误码
 */
log_error_t log_clear_statistics(log_handle_t *handle);

/**
 * @brief 获取模块版本信息
 * <AUTHOR>
 * @date 2025-08-04
 * @param major 主版本号指针
 * @param minor 次版本号指针
 * @param patch 补丁版本号指针
 */
void log_get_version(uint8_t *major, uint8_t *minor, uint8_t *patch);

/* 便捷宏定义 -----------------------------------------------------------------*/

/* 全局日志句柄声明（需要在应用代码中定义） */
extern log_handle_t g_log_handle;

/* 日志输出宏 - 带文件名、函数名和行号信息 */
#define LOG_DEBUG(format, ...) \
    log_output(&g_log_handle, LOG_LEVEL_DEBUG, __FILE__, __FUNCTION__, __LINE__, format, ##__VA_ARGS__)

#define LOG_INFO(format, ...) \
    log_output(&g_log_handle, LOG_LEVEL_INFO, __FILE__, __FUNCTION__, __LINE__, format, ##__VA_ARGS__)

#define LOG_WARN(format, ...) \
    log_output(&g_log_handle, LOG_LEVEL_WARN, __FILE__, __FUNCTION__, __LINE__, format, ##__VA_ARGS__)

#define LOG_ERROR(format, ...) \
    log_output(&g_log_handle, LOG_LEVEL_ERROR, __FILE__, __FUNCTION__, __LINE__, format, ##__VA_ARGS__)

/* 简化的日志输出宏 - 不带文件信息 */
#define LOGD(format, ...) \
    log_output(&g_log_handle, LOG_LEVEL_DEBUG, NULL, NULL, 0, format, ##__VA_ARGS__)

#define LOGI(format, ...) \
    log_output(&g_log_handle, LOG_LEVEL_INFO, NULL, NULL, 0, format, ##__VA_ARGS__)

#define LOGW(format, ...) \
    log_output(&g_log_handle, LOG_LEVEL_WARN, NULL, NULL, 0, format, ##__VA_ARGS__)

#define LOGE(format, ...) \
    log_output(&g_log_handle, LOG_LEVEL_ERROR, NULL, NULL, 0, format, ##__VA_ARGS__)

/* 条件日志输出宏 */
#define LOG_DEBUG_IF(condition, format, ...) \
    do { if (condition) LOG_DEBUG(format, ##__VA_ARGS__); } while(0)

#define LOG_INFO_IF(condition, format, ...) \
    do { if (condition) LOG_INFO(format, ##__VA_ARGS__); } while(0)

#define LOG_WARN_IF(condition, format, ...) \
    do { if (condition) LOG_WARN(format, ##__VA_ARGS__); } while(0)

#define LOG_ERROR_IF(condition, format, ...) \
    do { if (condition) LOG_ERROR(format, ##__VA_ARGS__); } while(0)

#ifdef __cplusplus
}
#endif

#endif /* __LOG_MODULE_H */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
