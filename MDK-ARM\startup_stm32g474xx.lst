


ARM Macro Assembler    Page 1 


    1 00000000         ;*******************************************************
                       ************************
    2 00000000         ;* @File Name          : startup_stm32g474xx.s
    3 00000000         ;* <AUTHOR> MCD Application Team
    4 00000000         ;* @Brief              : Vector table for MDK-ARM toolch
                       ain
    5 00000000         ;*******************************************************
                       ************************
    6 00000000         ;* Description        : STM32G474xx Mainstream devices v
                       ector table for
    7 00000000         ;*                      MDK-ARM toolchain.
    8 00000000         ;*                      This module performs:
    9 00000000         ;*                      - Set the initial SP
   10 00000000         ;*                      - Set the initial PC == Reset_Ha
                       ndler
   11 00000000         ;*                      - Set the vector table entries w
                       ith the exceptions ISR address
   12 00000000         ;*                      - Branches to __main in the C li
                       brary (which eventually
   13 00000000         ;*                        calls main()).
   14 00000000         ;*                      After Reset the Cortex-M4 proces
                       sor is in Thread mode,
   15 00000000         ;*                      priority is Privileged, and the 
                       Stack is set to Main.
   16 00000000         ;*******************************************************
                       *************************
   17 00000000         ;* @attention
   18 00000000         ;*
   19 00000000         ;* <h2><center>&copy; Copyright (c) 2019 STMicroelectron
                       ics.
   20 00000000         ;* All rights reserved.</center></h2>
   21 00000000         ;*
   22 00000000         ;* This software component is licensed by ST under BSD 3
                       -Clause license,
   23 00000000         ;* the "License"; You may not use this file except in co
                       mpliance with the
   24 00000000         ;* License. You may obtain a copy of the License at:
   25 00000000         ;*                        opensource.org/licenses/BSD-3-
                       Clause
   26 00000000         ;*
   27 00000000         ;*******************************************************
                       ************************
   28 00000000         ;* <<< Use Configuration Wizard in Context Menu >>>
   29 00000000         ;
   30 00000000         ; Amount of memory (in bytes) allocated for Stack
   31 00000000         ; Tailor this value to your application needs
   32 00000000         ; <h> Stack Configuration
   33 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   34 00000000         ; </h>
   35 00000000         
   36 00000000 00000400 
                       Stack_Size
                               EQU              0x400
   37 00000000         
   38 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   39 00000000         Stack_Mem
                               SPACE            Stack_Size
   40 00000400         __initial_sp



ARM Macro Assembler    Page 2 


   41 00000400         
   42 00000400         
   43 00000400         ; <h> Heap Configuration
   44 00000400         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   45 00000400         ; </h>
   46 00000400         
   47 00000400 00000200 
                       Heap_Size
                               EQU              0x200
   48 00000400         
   49 00000400                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   50 00000000         __heap_base
   51 00000000         Heap_Mem
                               SPACE            Heap_Size
   52 00000200         __heap_limit
   53 00000200         
   54 00000200                 PRESERVE8
   55 00000200                 THUMB
   56 00000200         
   57 00000200         
   58 00000200         ; Vector Table Mapped to Address 0 at Reset
   59 00000200                 AREA             RESET, DATA, READONLY
   60 00000000                 EXPORT           __Vectors
   61 00000000                 EXPORT           __Vectors_End
   62 00000000                 EXPORT           __Vectors_Size
   63 00000000         
   64 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   65 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   66 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   67 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   68 00000010 00000000        DCD              MemManage_Handler 
                                                            ; MPU Fault Handler
                                                            
   69 00000014 00000000        DCD              BusFault_Handler 
                                                            ; Bus Fault Handler
                                                            
   70 00000018 00000000        DCD              UsageFault_Handler ; Usage Faul
                                                            t Handler
   71 0000001C 00000000        DCD              0           ; Reserved
   72 00000020 00000000        DCD              0           ; Reserved
   73 00000024 00000000        DCD              0           ; Reserved
   74 00000028 00000000        DCD              0           ; Reserved
   75 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   76 00000030 00000000        DCD              DebugMon_Handler ; Debug Monito
                                                            r Handler
   77 00000034 00000000        DCD              0           ; Reserved
   78 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   79 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   80 00000040         
   81 00000040         ; External Interrupts
   82 00000040 00000000        DCD              WWDG_IRQHandler 
                                                            ; Window WatchDog
   83 00000044 00000000        DCD              PVD_PVM_IRQHandler ; PVD/PVM1/P



ARM Macro Assembler    Page 3 


                                                            VM2/PVM3/PVM4 throu
                                                            gh EXTI Line detect
                                                            ion
   84 00000048 00000000        DCD              RTC_TAMP_LSECSS_IRQHandler ; RT
                                                            C, TAMP and RCC LSE
                                                            _CSS through the EX
                                                            TI line
   85 0000004C 00000000        DCD              RTC_WKUP_IRQHandler ; RTC Wakeu
                                                            p through the EXTI 
                                                            line
   86 00000050 00000000        DCD              FLASH_IRQHandler ; FLASH
   87 00000054 00000000        DCD              RCC_IRQHandler ; RCC
   88 00000058 00000000        DCD              EXTI0_IRQHandler ; EXTI Line0
   89 0000005C 00000000        DCD              EXTI1_IRQHandler ; EXTI Line1
   90 00000060 00000000        DCD              EXTI2_IRQHandler ; EXTI Line2
   91 00000064 00000000        DCD              EXTI3_IRQHandler ; EXTI Line3
   92 00000068 00000000        DCD              EXTI4_IRQHandler ; EXTI Line4
   93 0000006C 00000000        DCD              DMA1_Channel1_IRQHandler 
                                                            ; DMA1 Channel 1
   94 00000070 00000000        DCD              DMA1_Channel2_IRQHandler 
                                                            ; DMA1 Channel 2
   95 00000074 00000000        DCD              DMA1_Channel3_IRQHandler 
                                                            ; DMA1 Channel 3
   96 00000078 00000000        DCD              DMA1_Channel4_IRQHandler 
                                                            ; DMA1 Channel 4
   97 0000007C 00000000        DCD              DMA1_Channel5_IRQHandler 
                                                            ; DMA1 Channel 5
   98 00000080 00000000        DCD              DMA1_Channel6_IRQHandler 
                                                            ; DMA1 Channel 6
   99 00000084 00000000        DCD              DMA1_Channel7_IRQHandler 
                                                            ; DMA1 Channel 7
  100 00000088 00000000        DCD              ADC1_2_IRQHandler 
                                                            ; ADC1 and ADC2
  101 0000008C 00000000        DCD              USB_HP_IRQHandler ; USB Device 
                                                            High Priority
  102 00000090 00000000        DCD              USB_LP_IRQHandler ; USB Device 
                                                            Low Priority
  103 00000094 00000000        DCD              FDCAN1_IT0_IRQHandler ; FDCAN1 
                                                            interrupt line 0
  104 00000098 00000000        DCD              FDCAN1_IT1_IRQHandler ; FDCAN1 
                                                            interrupt line 1
  105 0000009C 00000000        DCD              EXTI9_5_IRQHandler ; External L
                                                            ine[9:5]s
  106 000000A0 00000000        DCD              TIM1_BRK_TIM15_IRQHandler ; TIM
                                                            1 Break, Transition
                                                             error, Index error
                                                             and TIM15
  107 000000A4 00000000        DCD              TIM1_UP_TIM16_IRQHandler ; TIM1
                                                             Update and TIM16
  108 000000A8 00000000        DCD              TIM1_TRG_COM_TIM17_IRQHandler ;
                                                             TIM1 Trigger, Comm
                                                            utation, Direction 
                                                            change, Index and T
                                                            IM17
  109 000000AC 00000000        DCD              TIM1_CC_IRQHandler ; TIM1 Captu
                                                            re Compare
  110 000000B0 00000000        DCD              TIM2_IRQHandler ; TIM2
  111 000000B4 00000000        DCD              TIM3_IRQHandler ; TIM3
  112 000000B8 00000000        DCD              TIM4_IRQHandler ; TIM4



ARM Macro Assembler    Page 4 


  113 000000BC 00000000        DCD              I2C1_EV_IRQHandler ; I2C1 Event
                                                            
  114 000000C0 00000000        DCD              I2C1_ER_IRQHandler ; I2C1 Error
                                                            
  115 000000C4 00000000        DCD              I2C2_EV_IRQHandler ; I2C2 Event
                                                            
  116 000000C8 00000000        DCD              I2C2_ER_IRQHandler ; I2C2 Error
                                                            
  117 000000CC 00000000        DCD              SPI1_IRQHandler ; SPI1
  118 000000D0 00000000        DCD              SPI2_IRQHandler ; SPI2
  119 000000D4 00000000        DCD              USART1_IRQHandler ; USART1
  120 000000D8 00000000        DCD              USART2_IRQHandler ; USART2
  121 000000DC 00000000        DCD              USART3_IRQHandler ; USART3
  122 000000E0 00000000        DCD              EXTI15_10_IRQHandler ; External
                                                             Line[15:10]
  123 000000E4 00000000        DCD              RTC_Alarm_IRQHandler ; RTC Alar
                                                            m (A and B) through
                                                             EXTI Line
  124 000000E8 00000000        DCD              USBWakeUp_IRQHandler ; USB Wake
                                                            up through EXTI lin
                                                            e
  125 000000EC 00000000        DCD              TIM8_BRK_IRQHandler ; TIM8 Brea
                                                            k, Transition error
                                                             and Index error In
                                                            terrupt
  126 000000F0 00000000        DCD              TIM8_UP_IRQHandler ; TIM8 Updat
                                                            e Interrupt
  127 000000F4 00000000        DCD              TIM8_TRG_COM_IRQHandler ; TIM8 
                                                            Trigger, Commutatio
                                                            n, Direction change
                                                             and Index Interrup
                                                            t
  128 000000F8 00000000        DCD              TIM8_CC_IRQHandler ; TIM8 Captu
                                                            re Compare Interrup
                                                            t
  129 000000FC 00000000        DCD              ADC3_IRQHandler ; ADC3
  130 00000100 00000000        DCD              FMC_IRQHandler ; FMC
  131 00000104 00000000        DCD              LPTIM1_IRQHandler 
                                                            ; LP TIM1 interrupt
                                                            
  132 00000108 00000000        DCD              TIM5_IRQHandler ; TIM5
  133 0000010C 00000000        DCD              SPI3_IRQHandler ; SPI3
  134 00000110 00000000        DCD              UART4_IRQHandler ; UART4
  135 00000114 00000000        DCD              UART5_IRQHandler ; UART5
  136 00000118 00000000        DCD              TIM6_DAC_IRQHandler ; TIM6 and 
                                                            DAC1&3 underrun err
                                                            ors
  137 0000011C 00000000        DCD              TIM7_DAC_IRQHandler ; TIM7 and 
                                                            DAC2&4 underrun err
                                                            ors
  138 00000120 00000000        DCD              DMA2_Channel1_IRQHandler 
                                                            ; DMA2 Channel 1
  139 00000124 00000000        DCD              DMA2_Channel2_IRQHandler 
                                                            ; DMA2 Channel 2
  140 00000128 00000000        DCD              DMA2_Channel3_IRQHandler 
                                                            ; DMA2 Channel 3
  141 0000012C 00000000        DCD              DMA2_Channel4_IRQHandler 
                                                            ; DMA2 Channel 4
  142 00000130 00000000        DCD              DMA2_Channel5_IRQHandler 



ARM Macro Assembler    Page 5 


                                                            ; DMA2 Channel 5
  143 00000134 00000000        DCD              ADC4_IRQHandler ; ADC4
  144 00000138 00000000        DCD              ADC5_IRQHandler ; ADC5
  145 0000013C 00000000        DCD              UCPD1_IRQHandler ; UCPD1
  146 00000140 00000000        DCD              COMP1_2_3_IRQHandler ; COMP1, C
                                                            OMP2 and COMP3
  147 00000144 00000000        DCD              COMP4_5_6_IRQHandler ; COMP4, C
                                                            OMP5 and COMP6
  148 00000148 00000000        DCD              COMP7_IRQHandler ; COMP7
  149 0000014C 00000000        DCD              HRTIM1_Master_IRQHandler ; HRTI
                                                            M Master Timer glob
                                                            al Interrupts
  150 00000150 00000000        DCD              HRTIM1_TIMA_IRQHandler ; HRTIM 
                                                            Timer A global Inte
                                                            rrupt
  151 00000154 00000000        DCD              HRTIM1_TIMB_IRQHandler ; HRTIM 
                                                            Timer B global Inte
                                                            rrupt
  152 00000158 00000000        DCD              HRTIM1_TIMC_IRQHandler ; HRTIM 
                                                            Timer C global Inte
                                                            rrupt
  153 0000015C 00000000        DCD              HRTIM1_TIMD_IRQHandler ; HRTIM 
                                                            Timer D global Inte
                                                            rrupt
  154 00000160 00000000        DCD              HRTIM1_TIME_IRQHandler ; HRTIM 
                                                            Timer E global Inte
                                                            rrupt
  155 00000164 00000000        DCD              HRTIM1_FLT_IRQHandler ; HRTIM F
                                                            ault global Interru
                                                            pt
  156 00000168 00000000        DCD              HRTIM1_TIMF_IRQHandler ; HRTIM 
                                                            Timer F global Inte
                                                            rrupt
  157 0000016C 00000000        DCD              CRS_IRQHandler ; CRS Interrupt
  158 00000170 00000000        DCD              SAI1_IRQHandler ; Serial Audio 
                                                            Interface 1 global 
                                                            interrupt
  159 00000174 00000000        DCD              TIM20_BRK_IRQHandler ; TIM20 Br
                                                            eak, Transition err
                                                            or and Index error
  160 00000178 00000000        DCD              TIM20_UP_IRQHandler 
                                                            ; TIM20 Update
  161 0000017C 00000000        DCD              TIM20_TRG_COM_IRQHandler ; TIM2
                                                            0 Trigger, Commutat
                                                            ion, Direction chan
                                                            ge and Index
  162 00000180 00000000        DCD              TIM20_CC_IRQHandler ; TIM20 Cap
                                                            ture Compare
  163 00000184 00000000        DCD              FPU_IRQHandler ; FPU
  164 00000188 00000000        DCD              I2C4_EV_IRQHandler ; I2C4 event
                                                            
  165 0000018C 00000000        DCD              I2C4_ER_IRQHandler ; I2C4 error
                                                            
  166 00000190 00000000        DCD              SPI4_IRQHandler ; SPI4
  167 00000194 00000000        DCD              0           ; Reserved
  168 00000198 00000000        DCD              FDCAN2_IT0_IRQHandler ; FDCAN2 
                                                            interrupt line 0
  169 0000019C 00000000        DCD              FDCAN2_IT1_IRQHandler ; FDCAN2 
                                                            interrupt line 1



ARM Macro Assembler    Page 6 


  170 000001A0 00000000        DCD              FDCAN3_IT0_IRQHandler ; FDCAN3 
                                                            interrupt line 0
  171 000001A4 00000000        DCD              FDCAN3_IT1_IRQHandler ; FDCAN3 
                                                            interrupt line 1
  172 000001A8 00000000        DCD              RNG_IRQHandler ; RNG global int
                                                            errupt
  173 000001AC 00000000        DCD              LPUART1_IRQHandler ; LP UART 1 
                                                            interrupt
  174 000001B0 00000000        DCD              I2C3_EV_IRQHandler ; I2C3 Event
                                                            
  175 000001B4 00000000        DCD              I2C3_ER_IRQHandler ; I2C3 Error
                                                            
  176 000001B8 00000000        DCD              DMAMUX_OVR_IRQHandler ; DMAMUX 
                                                            overrun global inte
                                                            rrupt
  177 000001BC 00000000        DCD              QUADSPI_IRQHandler ; QUADSPI
  178 000001C0 00000000        DCD              DMA1_Channel8_IRQHandler 
                                                            ; DMA1 Channel 8
  179 000001C4 00000000        DCD              DMA2_Channel6_IRQHandler 
                                                            ; DMA2 Channel 6
  180 000001C8 00000000        DCD              DMA2_Channel7_IRQHandler 
                                                            ; DMA2 Channel 7
  181 000001CC 00000000        DCD              DMA2_Channel8_IRQHandler 
                                                            ; DMA2 Channel 8
  182 000001D0 00000000        DCD              CORDIC_IRQHandler ; CORDIC
  183 000001D4 00000000        DCD              FMAC_IRQHandler ; FMAC
  184 000001D8         
  185 000001D8         __Vectors_End
  186 000001D8         
  187 000001D8 000001D8 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  188 000001D8         
  189 000001D8                 AREA             |.text|, CODE, READONLY
  190 00000000         
  191 00000000         ; Reset handler
  192 00000000         Reset_Handler
                               PROC
  193 00000000                 EXPORT           Reset_Handler             [WEAK
]
  194 00000000                 IMPORT           SystemInit
  195 00000000                 IMPORT           __main
  196 00000000         
  197 00000000 4809            LDR              R0, =SystemInit
  198 00000002 4780            BLX              R0
  199 00000004 4809            LDR              R0, =__main
  200 00000006 4700            BX               R0
  201 00000008                 ENDP
  202 00000008         
  203 00000008         ; Dummy Exception Handlers (infinite loops which can be 
                       modified)
  204 00000008         
  205 00000008         NMI_Handler
                               PROC
  206 00000008                 EXPORT           NMI_Handler                [WEA
K]
  207 00000008 E7FE            B                .
  208 0000000A                 ENDP
  210 0000000A         HardFault_Handler



ARM Macro Assembler    Page 7 


                               PROC
  211 0000000A                 EXPORT           HardFault_Handler          [WEA
K]
  212 0000000A E7FE            B                .
  213 0000000C                 ENDP
  215 0000000C         MemManage_Handler
                               PROC
  216 0000000C                 EXPORT           MemManage_Handler          [WEA
K]
  217 0000000C E7FE            B                .
  218 0000000E                 ENDP
  220 0000000E         BusFault_Handler
                               PROC
  221 0000000E                 EXPORT           BusFault_Handler           [WEA
K]
  222 0000000E E7FE            B                .
  223 00000010                 ENDP
  225 00000010         UsageFault_Handler
                               PROC
  226 00000010                 EXPORT           UsageFault_Handler         [WEA
K]
  227 00000010 E7FE            B                .
  228 00000012                 ENDP
  229 00000012         SVC_Handler
                               PROC
  230 00000012                 EXPORT           SVC_Handler                [WEA
K]
  231 00000012 E7FE            B                .
  232 00000014                 ENDP
  234 00000014         DebugMon_Handler
                               PROC
  235 00000014                 EXPORT           DebugMon_Handler           [WEA
K]
  236 00000014 E7FE            B                .
  237 00000016                 ENDP
  238 00000016         PendSV_Handler
                               PROC
  239 00000016                 EXPORT           PendSV_Handler             [WEA
K]
  240 00000016 E7FE            B                .
  241 00000018                 ENDP
  242 00000018         SysTick_Handler
                               PROC
  243 00000018                 EXPORT           SysTick_Handler            [WEA
K]
  244 00000018 E7FE            B                .
  245 0000001A                 ENDP
  246 0000001A         
  247 0000001A         Default_Handler
                               PROC
  248 0000001A         
  249 0000001A                 EXPORT           WWDG_IRQHandler                
   [WEAK]
  250 0000001A                 EXPORT           PVD_PVM_IRQHandler             
   [WEAK]
  251 0000001A                 EXPORT           RTC_TAMP_LSECSS_IRQHandler     
   [WEAK]
  252 0000001A                 EXPORT           RTC_WKUP_IRQHandler            
   [WEAK]



ARM Macro Assembler    Page 8 


  253 0000001A                 EXPORT           FLASH_IRQHandler               
   [WEAK]
  254 0000001A                 EXPORT           RCC_IRQHandler                 
   [WEAK]
  255 0000001A                 EXPORT           EXTI0_IRQHandler               
   [WEAK]
  256 0000001A                 EXPORT           EXTI1_IRQHandler               
   [WEAK]
  257 0000001A                 EXPORT           EXTI2_IRQHandler               
   [WEAK]
  258 0000001A                 EXPORT           EXTI3_IRQHandler               
   [WEAK]
  259 0000001A                 EXPORT           EXTI4_IRQHandler               
   [WEAK]
  260 0000001A                 EXPORT           DMA1_Channel1_IRQHandler       
   [WEAK]
  261 0000001A                 EXPORT           DMA1_Channel2_IRQHandler       
   [WEAK]
  262 0000001A                 EXPORT           DMA1_Channel3_IRQHandler       
   [WEAK]
  263 0000001A                 EXPORT           DMA1_Channel4_IRQHandler       
   [WEAK]
  264 0000001A                 EXPORT           DMA1_Channel5_IRQHandler       
   [WEAK]
  265 0000001A                 EXPORT           DMA1_Channel6_IRQHandler       
   [WEAK]
  266 0000001A                 EXPORT           DMA1_Channel7_IRQHandler       
   [WEAK]
  267 0000001A                 EXPORT           ADC1_2_IRQHandler              
   [WEAK]
  268 0000001A                 EXPORT           USB_HP_IRQHandler              
   [WEAK]
  269 0000001A                 EXPORT           USB_LP_IRQHandler              
   [WEAK]
  270 0000001A                 EXPORT           FDCAN1_IT0_IRQHandler          
   [WEAK]
  271 0000001A                 EXPORT           FDCAN1_IT1_IRQHandler          
   [WEAK]
  272 0000001A                 EXPORT           EXTI9_5_IRQHandler             
   [WEAK]
  273 0000001A                 EXPORT           TIM1_BRK_TIM15_IRQHandler      
   [WEAK]
  274 0000001A                 EXPORT           TIM1_UP_TIM16_IRQHandler       
   [WEAK]
  275 0000001A                 EXPORT           TIM1_TRG_COM_TIM17_IRQHandler  
   [WEAK]
  276 0000001A                 EXPORT           TIM1_CC_IRQHandler             
   [WEAK]
  277 0000001A                 EXPORT           TIM2_IRQHandler                
   [WEAK]
  278 0000001A                 EXPORT           TIM3_IRQHandler                
   [WEAK]
  279 0000001A                 EXPORT           TIM4_IRQHandler                
   [WEAK]
  280 0000001A                 EXPORT           I2C1_EV_IRQHandler             
   [WEAK]
  281 0000001A                 EXPORT           I2C1_ER_IRQHandler             
   [WEAK]
  282 0000001A                 EXPORT           I2C2_EV_IRQHandler             



ARM Macro Assembler    Page 9 


   [WEAK]
  283 0000001A                 EXPORT           I2C2_ER_IRQHandler             
   [WEAK]
  284 0000001A                 EXPORT           SPI1_IRQHandler                
   [WEAK]
  285 0000001A                 EXPORT           SPI2_IRQHandler                
   [WEAK]
  286 0000001A                 EXPORT           USART1_IRQHandler              
   [WEAK]
  287 0000001A                 EXPORT           USART2_IRQHandler              
   [WEAK]
  288 0000001A                 EXPORT           USART3_IRQHandler              
   [WEAK]
  289 0000001A                 EXPORT           EXTI15_10_IRQHandler           
   [WEAK]
  290 0000001A                 EXPORT           RTC_Alarm_IRQHandler           
   [WEAK]
  291 0000001A                 EXPORT           USBWakeUp_IRQHandler           
   [WEAK]
  292 0000001A                 EXPORT           TIM8_BRK_IRQHandler            
   [WEAK]
  293 0000001A                 EXPORT           TIM8_UP_IRQHandler             
   [WEAK]
  294 0000001A                 EXPORT           TIM8_TRG_COM_IRQHandler        
   [WEAK]
  295 0000001A                 EXPORT           TIM8_CC_IRQHandler             
   [WEAK]
  296 0000001A                 EXPORT           ADC3_IRQHandler                
   [WEAK]
  297 0000001A                 EXPORT           FMC_IRQHandler                 
   [WEAK]
  298 0000001A                 EXPORT           LPTIM1_IRQHandler              
   [WEAK]
  299 0000001A                 EXPORT           TIM5_IRQHandler                
   [WEAK]
  300 0000001A                 EXPORT           SPI3_IRQHandler                
   [WEAK]
  301 0000001A                 EXPORT           UART4_IRQHandler               
   [WEAK]
  302 0000001A                 EXPORT           UART5_IRQHandler               
   [WEAK]
  303 0000001A                 EXPORT           TIM6_DAC_IRQHandler            
   [WEAK]
  304 0000001A                 EXPORT           TIM7_DAC_IRQHandler            
   [WEAK]
  305 0000001A                 EXPORT           DMA2_Channel1_IRQHandler       
   [WEAK]
  306 0000001A                 EXPORT           DMA2_Channel2_IRQHandler       
   [WEAK]
  307 0000001A                 EXPORT           DMA2_Channel3_IRQHandler       
   [WEAK]
  308 0000001A                 EXPORT           DMA2_Channel4_IRQHandler       
   [WEAK]
  309 0000001A                 EXPORT           DMA2_Channel5_IRQHandler       
   [WEAK]
  310 0000001A                 EXPORT           ADC4_IRQHandler                
   [WEAK]
  311 0000001A                 EXPORT           ADC5_IRQHandler                
   [WEAK]



ARM Macro Assembler    Page 10 


  312 0000001A                 EXPORT           UCPD1_IRQHandler               
   [WEAK]
  313 0000001A                 EXPORT           COMP1_2_3_IRQHandler           
   [WEAK]
  314 0000001A                 EXPORT           COMP4_5_6_IRQHandler           
   [WEAK]
  315 0000001A                 EXPORT           COMP7_IRQHandler               
   [WEAK]
  316 0000001A                 EXPORT           HRTIM1_Master_IRQHandler       
   [WEAK]
  317 0000001A                 EXPORT           HRTIM1_TIMA_IRQHandler         
   [WEAK]
  318 0000001A                 EXPORT           HRTIM1_TIMB_IRQHandler         
   [WEAK]
  319 0000001A                 EXPORT           HRTIM1_TIMC_IRQHandler         
   [WEAK]
  320 0000001A                 EXPORT           HRTIM1_TIMD_IRQHandler         
   [WEAK]
  321 0000001A                 EXPORT           HRTIM1_TIME_IRQHandler         
   [WEAK]
  322 0000001A                 EXPORT           HRTIM1_FLT_IRQHandler          
   [WEAK]
  323 0000001A                 EXPORT           HRTIM1_TIMF_IRQHandler         
   [WEAK]
  324 0000001A                 EXPORT           CRS_IRQHandler                 
   [WEAK]
  325 0000001A                 EXPORT           SAI1_IRQHandler                
   [WEAK]
  326 0000001A                 EXPORT           TIM20_BRK_IRQHandler           
   [WEAK]
  327 0000001A                 EXPORT           TIM20_UP_IRQHandler            
   [WEAK]
  328 0000001A                 EXPORT           TIM20_TRG_COM_IRQHandler       
   [WEAK]
  329 0000001A                 EXPORT           TIM20_CC_IRQHandler            
   [WEAK]
  330 0000001A                 EXPORT           FPU_IRQHandler                 
   [WEAK]
  331 0000001A                 EXPORT           I2C4_EV_IRQHandler             
   [WEAK]
  332 0000001A                 EXPORT           I2C4_ER_IRQHandler             
   [WEAK]
  333 0000001A                 EXPORT           SPI4_IRQHandler                
   [WEAK]
  334 0000001A                 EXPORT           FDCAN2_IT0_IRQHandler          
   [WEAK]
  335 0000001A                 EXPORT           FDCAN2_IT1_IRQHandler          
   [WEAK]
  336 0000001A                 EXPORT           FDCAN3_IT0_IRQHandler          
   [WEAK]
  337 0000001A                 EXPORT           FDCAN3_IT1_IRQHandler          
   [WEAK]
  338 0000001A                 EXPORT           RNG_IRQHandler                 
   [WEAK]
  339 0000001A                 EXPORT           LPUART1_IRQHandler             
   [WEAK]
  340 0000001A                 EXPORT           I2C3_EV_IRQHandler             
   [WEAK]
  341 0000001A                 EXPORT           I2C3_ER_IRQHandler             



ARM Macro Assembler    Page 11 


   [WEAK]
  342 0000001A                 EXPORT           DMAMUX_OVR_IRQHandler          
   [WEAK]
  343 0000001A                 EXPORT           QUADSPI_IRQHandler             
   [WEAK]
  344 0000001A                 EXPORT           DMA1_Channel8_IRQHandler       
   [WEAK]
  345 0000001A                 EXPORT           DMA2_Channel6_IRQHandler       
   [WEAK]
  346 0000001A                 EXPORT           DMA2_Channel7_IRQHandler       
   [WEAK]
  347 0000001A                 EXPORT           DMA2_Channel8_IRQHandler       
   [WEAK]
  348 0000001A                 EXPORT           CORDIC_IRQHandler              
   [WEAK]
  349 0000001A                 EXPORT           FMAC_IRQHandler                
   [WEAK]
  350 0000001A         
  351 0000001A         WWDG_IRQHandler
  352 0000001A         PVD_PVM_IRQHandler
  353 0000001A         RTC_TAMP_LSECSS_IRQHandler
  354 0000001A         RTC_WKUP_IRQHandler
  355 0000001A         FLASH_IRQHandler
  356 0000001A         RCC_IRQHandler
  357 0000001A         EXTI0_IRQHandler
  358 0000001A         EXTI1_IRQHandler
  359 0000001A         EXTI2_IRQHandler
  360 0000001A         EXTI3_IRQHandler
  361 0000001A         EXTI4_IRQHandler
  362 0000001A         DMA1_Channel1_IRQHandler
  363 0000001A         DMA1_Channel2_IRQHandler
  364 0000001A         DMA1_Channel3_IRQHandler
  365 0000001A         DMA1_Channel4_IRQHandler
  366 0000001A         DMA1_Channel5_IRQHandler
  367 0000001A         DMA1_Channel6_IRQHandler
  368 0000001A         DMA1_Channel7_IRQHandler
  369 0000001A         ADC1_2_IRQHandler
  370 0000001A         USB_HP_IRQHandler
  371 0000001A         USB_LP_IRQHandler
  372 0000001A         FDCAN1_IT0_IRQHandler
  373 0000001A         FDCAN1_IT1_IRQHandler
  374 0000001A         EXTI9_5_IRQHandler
  375 0000001A         TIM1_BRK_TIM15_IRQHandler
  376 0000001A         TIM1_UP_TIM16_IRQHandler
  377 0000001A         TIM1_TRG_COM_TIM17_IRQHandler
  378 0000001A         TIM1_CC_IRQHandler
  379 0000001A         TIM2_IRQHandler
  380 0000001A         TIM3_IRQHandler
  381 0000001A         TIM4_IRQHandler
  382 0000001A         I2C1_EV_IRQHandler
  383 0000001A         I2C1_ER_IRQHandler
  384 0000001A         I2C2_EV_IRQHandler
  385 0000001A         I2C2_ER_IRQHandler
  386 0000001A         SPI1_IRQHandler
  387 0000001A         SPI2_IRQHandler
  388 0000001A         USART1_IRQHandler
  389 0000001A         USART2_IRQHandler
  390 0000001A         USART3_IRQHandler
  391 0000001A         EXTI15_10_IRQHandler



ARM Macro Assembler    Page 12 


  392 0000001A         RTC_Alarm_IRQHandler
  393 0000001A         USBWakeUp_IRQHandler
  394 0000001A         TIM8_BRK_IRQHandler
  395 0000001A         TIM8_UP_IRQHandler
  396 0000001A         TIM8_TRG_COM_IRQHandler
  397 0000001A         TIM8_CC_IRQHandler
  398 0000001A         ADC3_IRQHandler
  399 0000001A         FMC_IRQHandler
  400 0000001A         LPTIM1_IRQHandler
  401 0000001A         TIM5_IRQHandler
  402 0000001A         SPI3_IRQHandler
  403 0000001A         UART4_IRQHandler
  404 0000001A         UART5_IRQHandler
  405 0000001A         TIM6_DAC_IRQHandler
  406 0000001A         TIM7_DAC_IRQHandler
  407 0000001A         DMA2_Channel1_IRQHandler
  408 0000001A         DMA2_Channel2_IRQHandler
  409 0000001A         DMA2_Channel3_IRQHandler
  410 0000001A         DMA2_Channel4_IRQHandler
  411 0000001A         DMA2_Channel5_IRQHandler
  412 0000001A         ADC4_IRQHandler
  413 0000001A         ADC5_IRQHandler
  414 0000001A         UCPD1_IRQHandler
  415 0000001A         COMP1_2_3_IRQHandler
  416 0000001A         COMP4_5_6_IRQHandler
  417 0000001A         COMP7_IRQHandler
  418 0000001A         HRTIM1_Master_IRQHandler
  419 0000001A         HRTIM1_TIMA_IRQHandler
  420 0000001A         HRTIM1_TIMB_IRQHandler
  421 0000001A         HRTIM1_TIMC_IRQHandler
  422 0000001A         HRTIM1_TIMD_IRQHandler
  423 0000001A         HRTIM1_TIME_IRQHandler
  424 0000001A         HRTIM1_FLT_IRQHandler
  425 0000001A         HRTIM1_TIMF_IRQHandler
  426 0000001A         CRS_IRQHandler
  427 0000001A         SAI1_IRQHandler
  428 0000001A         TIM20_BRK_IRQHandler
  429 0000001A         TIM20_UP_IRQHandler
  430 0000001A         TIM20_TRG_COM_IRQHandler
  431 0000001A         TIM20_CC_IRQHandler
  432 0000001A         FPU_IRQHandler
  433 0000001A         I2C4_EV_IRQHandler
  434 0000001A         I2C4_ER_IRQHandler
  435 0000001A         SPI4_IRQHandler
  436 0000001A         FDCAN2_IT0_IRQHandler
  437 0000001A         FDCAN2_IT1_IRQHandler
  438 0000001A         FDCAN3_IT0_IRQHandler
  439 0000001A         FDCAN3_IT1_IRQHandler
  440 0000001A         RNG_IRQHandler
  441 0000001A         LPUART1_IRQHandler
  442 0000001A         I2C3_EV_IRQHandler
  443 0000001A         I2C3_ER_IRQHandler
  444 0000001A         DMAMUX_OVR_IRQHandler
  445 0000001A         QUADSPI_IRQHandler
  446 0000001A         DMA1_Channel8_IRQHandler
  447 0000001A         DMA2_Channel6_IRQHandler
  448 0000001A         DMA2_Channel7_IRQHandler
  449 0000001A         DMA2_Channel8_IRQHandler
  450 0000001A         CORDIC_IRQHandler



ARM Macro Assembler    Page 13 


  451 0000001A         FMAC_IRQHandler
  452 0000001A         
  453 0000001A E7FE            B                .
  454 0000001C         
  455 0000001C                 ENDP
  456 0000001C         
  457 0000001C                 ALIGN
  458 0000001C         
  459 0000001C         ;*******************************************************
                       ************************
  460 0000001C         ; User Stack and Heap initialization
  461 0000001C         ;*******************************************************
                       ************************
  462 0000001C                 IF               :DEF:__MICROLIB
  469 0000001C         
  470 0000001C                 IMPORT           __use_two_region_memory
  471 0000001C                 EXPORT           __user_initial_stackheap
  472 0000001C         
  473 0000001C         __user_initial_stackheap
  474 0000001C         
  475 0000001C 4804            LDR              R0, =  Heap_Mem
  476 0000001E 4905            LDR              R1, =(Stack_Mem + Stack_Size)
  477 00000020 4A05            LDR              R2, = (Heap_Mem +  Heap_Size)
  478 00000022 4B06            LDR              R3, = Stack_Mem
  479 00000024 4770            BX               LR
  480 00000026         
  481 00000026 00 00           ALIGN
  482 00000028         
  483 00000028                 ENDIF
  484 00000028         
  485 00000028                 END
              00000000 
              00000000 
              00000000 
              00000400 
              00000200 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M4.fp.sp --apcs=
interwork --depend=stm32g4\startup_stm32g474xx.d -ostm32g4\startup_stm32g474xx.
o -I..\Core\Inc -I.\RTE\_STM32G4 -IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\
CMSIS\5.7.0\CMSIS\Core\Include --predefine="__UVISION_VERSION SETA 534" --prede
fine="_RTE_ SETA 1" --predefine="STM32G474xx SETA 1" --predefine="_RTE_ SETA 1"
 --list=startup_stm32g474xx.lst startup_stm32g474xx.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 38 in file startup_stm32g474xx.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 39 in file startup_stm32g474xx.s
   Uses
      At line 476 in file startup_stm32g474xx.s
      At line 478 in file startup_stm32g474xx.s

__initial_sp 00000400

Symbol: __initial_sp
   Definitions
      At line 40 in file startup_stm32g474xx.s
   Uses
      At line 64 in file startup_stm32g474xx.s
Comment: __initial_sp used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 49 in file startup_stm32g474xx.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 51 in file startup_stm32g474xx.s
   Uses
      At line 475 in file startup_stm32g474xx.s
      At line 477 in file startup_stm32g474xx.s

__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 50 in file startup_stm32g474xx.s
   Uses
      None
Comment: __heap_base unused
__heap_limit 00000200

Symbol: __heap_limit
   Definitions
      At line 52 in file startup_stm32g474xx.s
   Uses
      None
Comment: __heap_limit unused
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 59 in file startup_stm32g474xx.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 64 in file startup_stm32g474xx.s
   Uses
      At line 60 in file startup_stm32g474xx.s
      At line 187 in file startup_stm32g474xx.s

__Vectors_End 000001D8

Symbol: __Vectors_End
   Definitions
      At line 185 in file startup_stm32g474xx.s
   Uses
      At line 61 in file startup_stm32g474xx.s
      At line 187 in file startup_stm32g474xx.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 189 in file startup_stm32g474xx.s
   Uses
      None
Comment: .text unused
ADC1_2_IRQHandler 0000001A

Symbol: ADC1_2_IRQHandler
   Definitions
      At line 369 in file startup_stm32g474xx.s
   Uses
      At line 100 in file startup_stm32g474xx.s
      At line 267 in file startup_stm32g474xx.s

ADC3_IRQHandler 0000001A

Symbol: ADC3_IRQHandler
   Definitions
      At line 398 in file startup_stm32g474xx.s
   Uses
      At line 129 in file startup_stm32g474xx.s
      At line 296 in file startup_stm32g474xx.s

ADC4_IRQHandler 0000001A

Symbol: ADC4_IRQHandler
   Definitions
      At line 412 in file startup_stm32g474xx.s
   Uses
      At line 143 in file startup_stm32g474xx.s
      At line 310 in file startup_stm32g474xx.s

ADC5_IRQHandler 0000001A

Symbol: ADC5_IRQHandler
   Definitions
      At line 413 in file startup_stm32g474xx.s
   Uses
      At line 144 in file startup_stm32g474xx.s
      At line 311 in file startup_stm32g474xx.s

BusFault_Handler 0000000E

Symbol: BusFault_Handler
   Definitions
      At line 220 in file startup_stm32g474xx.s
   Uses
      At line 69 in file startup_stm32g474xx.s
      At line 221 in file startup_stm32g474xx.s

COMP1_2_3_IRQHandler 0000001A

Symbol: COMP1_2_3_IRQHandler
   Definitions
      At line 415 in file startup_stm32g474xx.s
   Uses



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 146 in file startup_stm32g474xx.s
      At line 313 in file startup_stm32g474xx.s

COMP4_5_6_IRQHandler 0000001A

Symbol: COMP4_5_6_IRQHandler
   Definitions
      At line 416 in file startup_stm32g474xx.s
   Uses
      At line 147 in file startup_stm32g474xx.s
      At line 314 in file startup_stm32g474xx.s

COMP7_IRQHandler 0000001A

Symbol: COMP7_IRQHandler
   Definitions
      At line 417 in file startup_stm32g474xx.s
   Uses
      At line 148 in file startup_stm32g474xx.s
      At line 315 in file startup_stm32g474xx.s

CORDIC_IRQHandler 0000001A

Symbol: CORDIC_IRQHandler
   Definitions
      At line 450 in file startup_stm32g474xx.s
   Uses
      At line 182 in file startup_stm32g474xx.s
      At line 348 in file startup_stm32g474xx.s

CRS_IRQHandler 0000001A

Symbol: CRS_IRQHandler
   Definitions
      At line 426 in file startup_stm32g474xx.s
   Uses
      At line 157 in file startup_stm32g474xx.s
      At line 324 in file startup_stm32g474xx.s

DMA1_Channel1_IRQHandler 0000001A

Symbol: DMA1_Channel1_IRQHandler
   Definitions
      At line 362 in file startup_stm32g474xx.s
   Uses
      At line 93 in file startup_stm32g474xx.s
      At line 260 in file startup_stm32g474xx.s

DMA1_Channel2_IRQHandler 0000001A

Symbol: DMA1_Channel2_IRQHandler
   Definitions
      At line 363 in file startup_stm32g474xx.s
   Uses
      At line 94 in file startup_stm32g474xx.s
      At line 261 in file startup_stm32g474xx.s

DMA1_Channel3_IRQHandler 0000001A




ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

Symbol: DMA1_Channel3_IRQHandler
   Definitions
      At line 364 in file startup_stm32g474xx.s
   Uses
      At line 95 in file startup_stm32g474xx.s
      At line 262 in file startup_stm32g474xx.s

DMA1_Channel4_IRQHandler 0000001A

Symbol: DMA1_Channel4_IRQHandler
   Definitions
      At line 365 in file startup_stm32g474xx.s
   Uses
      At line 96 in file startup_stm32g474xx.s
      At line 263 in file startup_stm32g474xx.s

DMA1_Channel5_IRQHandler 0000001A

Symbol: DMA1_Channel5_IRQHandler
   Definitions
      At line 366 in file startup_stm32g474xx.s
   Uses
      At line 97 in file startup_stm32g474xx.s
      At line 264 in file startup_stm32g474xx.s

DMA1_Channel6_IRQHandler 0000001A

Symbol: DMA1_Channel6_IRQHandler
   Definitions
      At line 367 in file startup_stm32g474xx.s
   Uses
      At line 98 in file startup_stm32g474xx.s
      At line 265 in file startup_stm32g474xx.s

DMA1_Channel7_IRQHandler 0000001A

Symbol: DMA1_Channel7_IRQHandler
   Definitions
      At line 368 in file startup_stm32g474xx.s
   Uses
      At line 99 in file startup_stm32g474xx.s
      At line 266 in file startup_stm32g474xx.s

DMA1_Channel8_IRQHandler 0000001A

Symbol: DMA1_Channel8_IRQHandler
   Definitions
      At line 446 in file startup_stm32g474xx.s
   Uses
      At line 178 in file startup_stm32g474xx.s
      At line 344 in file startup_stm32g474xx.s

DMA2_Channel1_IRQHandler 0000001A

Symbol: DMA2_Channel1_IRQHandler
   Definitions
      At line 407 in file startup_stm32g474xx.s
   Uses
      At line 138 in file startup_stm32g474xx.s



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols

      At line 305 in file startup_stm32g474xx.s

DMA2_Channel2_IRQHandler 0000001A

Symbol: DMA2_Channel2_IRQHandler
   Definitions
      At line 408 in file startup_stm32g474xx.s
   Uses
      At line 139 in file startup_stm32g474xx.s
      At line 306 in file startup_stm32g474xx.s

DMA2_Channel3_IRQHandler 0000001A

Symbol: DMA2_Channel3_IRQHandler
   Definitions
      At line 409 in file startup_stm32g474xx.s
   Uses
      At line 140 in file startup_stm32g474xx.s
      At line 307 in file startup_stm32g474xx.s

DMA2_Channel4_IRQHandler 0000001A

Symbol: DMA2_Channel4_IRQHandler
   Definitions
      At line 410 in file startup_stm32g474xx.s
   Uses
      At line 141 in file startup_stm32g474xx.s
      At line 308 in file startup_stm32g474xx.s

DMA2_Channel5_IRQHandler 0000001A

Symbol: DMA2_Channel5_IRQHandler
   Definitions
      At line 411 in file startup_stm32g474xx.s
   Uses
      At line 142 in file startup_stm32g474xx.s
      At line 309 in file startup_stm32g474xx.s

DMA2_Channel6_IRQHandler 0000001A

Symbol: DMA2_Channel6_IRQHandler
   Definitions
      At line 447 in file startup_stm32g474xx.s
   Uses
      At line 179 in file startup_stm32g474xx.s
      At line 345 in file startup_stm32g474xx.s

DMA2_Channel7_IRQHandler 0000001A

Symbol: DMA2_Channel7_IRQHandler
   Definitions
      At line 448 in file startup_stm32g474xx.s
   Uses
      At line 180 in file startup_stm32g474xx.s
      At line 346 in file startup_stm32g474xx.s

DMA2_Channel8_IRQHandler 0000001A

Symbol: DMA2_Channel8_IRQHandler



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 449 in file startup_stm32g474xx.s
   Uses
      At line 181 in file startup_stm32g474xx.s
      At line 347 in file startup_stm32g474xx.s

DMAMUX_OVR_IRQHandler 0000001A

Symbol: DMAMUX_OVR_IRQHandler
   Definitions
      At line 444 in file startup_stm32g474xx.s
   Uses
      At line 176 in file startup_stm32g474xx.s
      At line 342 in file startup_stm32g474xx.s

DebugMon_Handler 00000014

Symbol: DebugMon_Handler
   Definitions
      At line 234 in file startup_stm32g474xx.s
   Uses
      At line 76 in file startup_stm32g474xx.s
      At line 235 in file startup_stm32g474xx.s

Default_Handler 0000001A

Symbol: Default_Handler
   Definitions
      At line 247 in file startup_stm32g474xx.s
   Uses
      None
Comment: Default_Handler unused
EXTI0_IRQHandler 0000001A

Symbol: EXTI0_IRQHandler
   Definitions
      At line 357 in file startup_stm32g474xx.s
   Uses
      At line 88 in file startup_stm32g474xx.s
      At line 255 in file startup_stm32g474xx.s

EXTI15_10_IRQHandler 0000001A

Symbol: EXTI15_10_IRQHandler
   Definitions
      At line 391 in file startup_stm32g474xx.s
   Uses
      At line 122 in file startup_stm32g474xx.s
      At line 289 in file startup_stm32g474xx.s

EXTI1_IRQHandler 0000001A

Symbol: EXTI1_IRQHandler
   Definitions
      At line 358 in file startup_stm32g474xx.s
   Uses
      At line 89 in file startup_stm32g474xx.s
      At line 256 in file startup_stm32g474xx.s




ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols

EXTI2_IRQHandler 0000001A

Symbol: EXTI2_IRQHandler
   Definitions
      At line 359 in file startup_stm32g474xx.s
   Uses
      At line 90 in file startup_stm32g474xx.s
      At line 257 in file startup_stm32g474xx.s

EXTI3_IRQHandler 0000001A

Symbol: EXTI3_IRQHandler
   Definitions
      At line 360 in file startup_stm32g474xx.s
   Uses
      At line 91 in file startup_stm32g474xx.s
      At line 258 in file startup_stm32g474xx.s

EXTI4_IRQHandler 0000001A

Symbol: EXTI4_IRQHandler
   Definitions
      At line 361 in file startup_stm32g474xx.s
   Uses
      At line 92 in file startup_stm32g474xx.s
      At line 259 in file startup_stm32g474xx.s

EXTI9_5_IRQHandler 0000001A

Symbol: EXTI9_5_IRQHandler
   Definitions
      At line 374 in file startup_stm32g474xx.s
   Uses
      At line 105 in file startup_stm32g474xx.s
      At line 272 in file startup_stm32g474xx.s

FDCAN1_IT0_IRQHandler 0000001A

Symbol: FDCAN1_IT0_IRQHandler
   Definitions
      At line 372 in file startup_stm32g474xx.s
   Uses
      At line 103 in file startup_stm32g474xx.s
      At line 270 in file startup_stm32g474xx.s

FDCAN1_IT1_IRQHandler 0000001A

Symbol: FDCAN1_IT1_IRQHandler
   Definitions
      At line 373 in file startup_stm32g474xx.s
   Uses
      At line 104 in file startup_stm32g474xx.s
      At line 271 in file startup_stm32g474xx.s

FDCAN2_IT0_IRQHandler 0000001A

Symbol: FDCAN2_IT0_IRQHandler
   Definitions
      At line 436 in file startup_stm32g474xx.s



ARM Macro Assembler    Page 7 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 168 in file startup_stm32g474xx.s
      At line 334 in file startup_stm32g474xx.s

FDCAN2_IT1_IRQHandler 0000001A

Symbol: FDCAN2_IT1_IRQHandler
   Definitions
      At line 437 in file startup_stm32g474xx.s
   Uses
      At line 169 in file startup_stm32g474xx.s
      At line 335 in file startup_stm32g474xx.s

FDCAN3_IT0_IRQHandler 0000001A

Symbol: FDCAN3_IT0_IRQHandler
   Definitions
      At line 438 in file startup_stm32g474xx.s
   Uses
      At line 170 in file startup_stm32g474xx.s
      At line 336 in file startup_stm32g474xx.s

FDCAN3_IT1_IRQHandler 0000001A

Symbol: FDCAN3_IT1_IRQHandler
   Definitions
      At line 439 in file startup_stm32g474xx.s
   Uses
      At line 171 in file startup_stm32g474xx.s
      At line 337 in file startup_stm32g474xx.s

FLASH_IRQHandler 0000001A

Symbol: FLASH_IRQHandler
   Definitions
      At line 355 in file startup_stm32g474xx.s
   Uses
      At line 86 in file startup_stm32g474xx.s
      At line 253 in file startup_stm32g474xx.s

FMAC_IRQHandler 0000001A

Symbol: FMAC_IRQHandler
   Definitions
      At line 451 in file startup_stm32g474xx.s
   Uses
      At line 183 in file startup_stm32g474xx.s
      At line 349 in file startup_stm32g474xx.s

FMC_IRQHandler 0000001A

Symbol: FMC_IRQHandler
   Definitions
      At line 399 in file startup_stm32g474xx.s
   Uses
      At line 130 in file startup_stm32g474xx.s
      At line 297 in file startup_stm32g474xx.s

FPU_IRQHandler 0000001A



ARM Macro Assembler    Page 8 Alphabetic symbol ordering
Relocatable symbols


Symbol: FPU_IRQHandler
   Definitions
      At line 432 in file startup_stm32g474xx.s
   Uses
      At line 163 in file startup_stm32g474xx.s
      At line 330 in file startup_stm32g474xx.s

HRTIM1_FLT_IRQHandler 0000001A

Symbol: HRTIM1_FLT_IRQHandler
   Definitions
      At line 424 in file startup_stm32g474xx.s
   Uses
      At line 155 in file startup_stm32g474xx.s
      At line 322 in file startup_stm32g474xx.s

HRTIM1_Master_IRQHandler 0000001A

Symbol: HRTIM1_Master_IRQHandler
   Definitions
      At line 418 in file startup_stm32g474xx.s
   Uses
      At line 149 in file startup_stm32g474xx.s
      At line 316 in file startup_stm32g474xx.s

HRTIM1_TIMA_IRQHandler 0000001A

Symbol: HRTIM1_TIMA_IRQHandler
   Definitions
      At line 419 in file startup_stm32g474xx.s
   Uses
      At line 150 in file startup_stm32g474xx.s
      At line 317 in file startup_stm32g474xx.s

HRTIM1_TIMB_IRQHandler 0000001A

Symbol: HRTIM1_TIMB_IRQHandler
   Definitions
      At line 420 in file startup_stm32g474xx.s
   Uses
      At line 151 in file startup_stm32g474xx.s
      At line 318 in file startup_stm32g474xx.s

HRTIM1_TIMC_IRQHandler 0000001A

Symbol: HRTIM1_TIMC_IRQHandler
   Definitions
      At line 421 in file startup_stm32g474xx.s
   Uses
      At line 152 in file startup_stm32g474xx.s
      At line 319 in file startup_stm32g474xx.s

HRTIM1_TIMD_IRQHandler 0000001A

Symbol: HRTIM1_TIMD_IRQHandler
   Definitions
      At line 422 in file startup_stm32g474xx.s
   Uses



ARM Macro Assembler    Page 9 Alphabetic symbol ordering
Relocatable symbols

      At line 153 in file startup_stm32g474xx.s
      At line 320 in file startup_stm32g474xx.s

HRTIM1_TIME_IRQHandler 0000001A

Symbol: HRTIM1_TIME_IRQHandler
   Definitions
      At line 423 in file startup_stm32g474xx.s
   Uses
      At line 154 in file startup_stm32g474xx.s
      At line 321 in file startup_stm32g474xx.s

HRTIM1_TIMF_IRQHandler 0000001A

Symbol: HRTIM1_TIMF_IRQHandler
   Definitions
      At line 425 in file startup_stm32g474xx.s
   Uses
      At line 156 in file startup_stm32g474xx.s
      At line 323 in file startup_stm32g474xx.s

HardFault_Handler 0000000A

Symbol: HardFault_Handler
   Definitions
      At line 210 in file startup_stm32g474xx.s
   Uses
      At line 67 in file startup_stm32g474xx.s
      At line 211 in file startup_stm32g474xx.s

I2C1_ER_IRQHandler 0000001A

Symbol: I2C1_ER_IRQHandler
   Definitions
      At line 383 in file startup_stm32g474xx.s
   Uses
      At line 114 in file startup_stm32g474xx.s
      At line 281 in file startup_stm32g474xx.s

I2C1_EV_IRQHandler 0000001A

Symbol: I2C1_EV_IRQHandler
   Definitions
      At line 382 in file startup_stm32g474xx.s
   Uses
      At line 113 in file startup_stm32g474xx.s
      At line 280 in file startup_stm32g474xx.s

I2C2_ER_IRQHandler 0000001A

Symbol: I2C2_ER_IRQHandler
   Definitions
      At line 385 in file startup_stm32g474xx.s
   Uses
      At line 116 in file startup_stm32g474xx.s
      At line 283 in file startup_stm32g474xx.s

I2C2_EV_IRQHandler 0000001A




ARM Macro Assembler    Page 10 Alphabetic symbol ordering
Relocatable symbols

Symbol: I2C2_EV_IRQHandler
   Definitions
      At line 384 in file startup_stm32g474xx.s
   Uses
      At line 115 in file startup_stm32g474xx.s
      At line 282 in file startup_stm32g474xx.s

I2C3_ER_IRQHandler 0000001A

Symbol: I2C3_ER_IRQHandler
   Definitions
      At line 443 in file startup_stm32g474xx.s
   Uses
      At line 175 in file startup_stm32g474xx.s
      At line 341 in file startup_stm32g474xx.s

I2C3_EV_IRQHandler 0000001A

Symbol: I2C3_EV_IRQHandler
   Definitions
      At line 442 in file startup_stm32g474xx.s
   Uses
      At line 174 in file startup_stm32g474xx.s
      At line 340 in file startup_stm32g474xx.s

I2C4_ER_IRQHandler 0000001A

Symbol: I2C4_ER_IRQHandler
   Definitions
      At line 434 in file startup_stm32g474xx.s
   Uses
      At line 165 in file startup_stm32g474xx.s
      At line 332 in file startup_stm32g474xx.s

I2C4_EV_IRQHandler 0000001A

Symbol: I2C4_EV_IRQHandler
   Definitions
      At line 433 in file startup_stm32g474xx.s
   Uses
      At line 164 in file startup_stm32g474xx.s
      At line 331 in file startup_stm32g474xx.s

LPTIM1_IRQHandler 0000001A

Symbol: LPTIM1_IRQHandler
   Definitions
      At line 400 in file startup_stm32g474xx.s
   Uses
      At line 131 in file startup_stm32g474xx.s
      At line 298 in file startup_stm32g474xx.s

LPUART1_IRQHandler 0000001A

Symbol: LPUART1_IRQHandler
   Definitions
      At line 441 in file startup_stm32g474xx.s
   Uses
      At line 173 in file startup_stm32g474xx.s



ARM Macro Assembler    Page 11 Alphabetic symbol ordering
Relocatable symbols

      At line 339 in file startup_stm32g474xx.s

MemManage_Handler 0000000C

Symbol: MemManage_Handler
   Definitions
      At line 215 in file startup_stm32g474xx.s
   Uses
      At line 68 in file startup_stm32g474xx.s
      At line 216 in file startup_stm32g474xx.s

NMI_Handler 00000008

Symbol: NMI_Handler
   Definitions
      At line 205 in file startup_stm32g474xx.s
   Uses
      At line 66 in file startup_stm32g474xx.s
      At line 206 in file startup_stm32g474xx.s

PVD_PVM_IRQHandler 0000001A

Symbol: PVD_PVM_IRQHandler
   Definitions
      At line 352 in file startup_stm32g474xx.s
   Uses
      At line 83 in file startup_stm32g474xx.s
      At line 250 in file startup_stm32g474xx.s

PendSV_Handler 00000016

Symbol: PendSV_Handler
   Definitions
      At line 238 in file startup_stm32g474xx.s
   Uses
      At line 78 in file startup_stm32g474xx.s
      At line 239 in file startup_stm32g474xx.s

QUADSPI_IRQHandler 0000001A

Symbol: QUADSPI_IRQHandler
   Definitions
      At line 445 in file startup_stm32g474xx.s
   Uses
      At line 177 in file startup_stm32g474xx.s
      At line 343 in file startup_stm32g474xx.s

RCC_IRQHandler 0000001A

Symbol: RCC_IRQHandler
   Definitions
      At line 356 in file startup_stm32g474xx.s
   Uses
      At line 87 in file startup_stm32g474xx.s
      At line 254 in file startup_stm32g474xx.s

RNG_IRQHandler 0000001A

Symbol: RNG_IRQHandler



ARM Macro Assembler    Page 12 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 440 in file startup_stm32g474xx.s
   Uses
      At line 172 in file startup_stm32g474xx.s
      At line 338 in file startup_stm32g474xx.s

RTC_Alarm_IRQHandler 0000001A

Symbol: RTC_Alarm_IRQHandler
   Definitions
      At line 392 in file startup_stm32g474xx.s
   Uses
      At line 123 in file startup_stm32g474xx.s
      At line 290 in file startup_stm32g474xx.s

RTC_TAMP_LSECSS_IRQHandler 0000001A

Symbol: RTC_TAMP_LSECSS_IRQHandler
   Definitions
      At line 353 in file startup_stm32g474xx.s
   Uses
      At line 84 in file startup_stm32g474xx.s
      At line 251 in file startup_stm32g474xx.s

RTC_WKUP_IRQHandler 0000001A

Symbol: RTC_WKUP_IRQHandler
   Definitions
      At line 354 in file startup_stm32g474xx.s
   Uses
      At line 85 in file startup_stm32g474xx.s
      At line 252 in file startup_stm32g474xx.s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 192 in file startup_stm32g474xx.s
   Uses
      At line 65 in file startup_stm32g474xx.s
      At line 193 in file startup_stm32g474xx.s

SAI1_IRQHandler 0000001A

Symbol: SAI1_IRQHandler
   Definitions
      At line 427 in file startup_stm32g474xx.s
   Uses
      At line 158 in file startup_stm32g474xx.s
      At line 325 in file startup_stm32g474xx.s

SPI1_IRQHandler 0000001A

Symbol: SPI1_IRQHandler
   Definitions
      At line 386 in file startup_stm32g474xx.s
   Uses
      At line 117 in file startup_stm32g474xx.s
      At line 284 in file startup_stm32g474xx.s



ARM Macro Assembler    Page 13 Alphabetic symbol ordering
Relocatable symbols


SPI2_IRQHandler 0000001A

Symbol: SPI2_IRQHandler
   Definitions
      At line 387 in file startup_stm32g474xx.s
   Uses
      At line 118 in file startup_stm32g474xx.s
      At line 285 in file startup_stm32g474xx.s

SPI3_IRQHandler 0000001A

Symbol: SPI3_IRQHandler
   Definitions
      At line 402 in file startup_stm32g474xx.s
   Uses
      At line 133 in file startup_stm32g474xx.s
      At line 300 in file startup_stm32g474xx.s

SPI4_IRQHandler 0000001A

Symbol: SPI4_IRQHandler
   Definitions
      At line 435 in file startup_stm32g474xx.s
   Uses
      At line 166 in file startup_stm32g474xx.s
      At line 333 in file startup_stm32g474xx.s

SVC_Handler 00000012

Symbol: SVC_Handler
   Definitions
      At line 229 in file startup_stm32g474xx.s
   Uses
      At line 75 in file startup_stm32g474xx.s
      At line 230 in file startup_stm32g474xx.s

SysTick_Handler 00000018

Symbol: SysTick_Handler
   Definitions
      At line 242 in file startup_stm32g474xx.s
   Uses
      At line 79 in file startup_stm32g474xx.s
      At line 243 in file startup_stm32g474xx.s

TIM1_BRK_TIM15_IRQHandler 0000001A

Symbol: TIM1_BRK_TIM15_IRQHandler
   Definitions
      At line 375 in file startup_stm32g474xx.s
   Uses
      At line 106 in file startup_stm32g474xx.s
      At line 273 in file startup_stm32g474xx.s

TIM1_CC_IRQHandler 0000001A

Symbol: TIM1_CC_IRQHandler
   Definitions



ARM Macro Assembler    Page 14 Alphabetic symbol ordering
Relocatable symbols

      At line 378 in file startup_stm32g474xx.s
   Uses
      At line 109 in file startup_stm32g474xx.s
      At line 276 in file startup_stm32g474xx.s

TIM1_TRG_COM_TIM17_IRQHandler 0000001A

Symbol: TIM1_TRG_COM_TIM17_IRQHandler
   Definitions
      At line 377 in file startup_stm32g474xx.s
   Uses
      At line 108 in file startup_stm32g474xx.s
      At line 275 in file startup_stm32g474xx.s

TIM1_UP_TIM16_IRQHandler 0000001A

Symbol: TIM1_UP_TIM16_IRQHandler
   Definitions
      At line 376 in file startup_stm32g474xx.s
   Uses
      At line 107 in file startup_stm32g474xx.s
      At line 274 in file startup_stm32g474xx.s

TIM20_BRK_IRQHandler 0000001A

Symbol: TIM20_BRK_IRQHandler
   Definitions
      At line 428 in file startup_stm32g474xx.s
   Uses
      At line 159 in file startup_stm32g474xx.s
      At line 326 in file startup_stm32g474xx.s

TIM20_CC_IRQHandler 0000001A

Symbol: TIM20_CC_IRQHandler
   Definitions
      At line 431 in file startup_stm32g474xx.s
   Uses
      At line 162 in file startup_stm32g474xx.s
      At line 329 in file startup_stm32g474xx.s

TIM20_TRG_COM_IRQHandler 0000001A

Symbol: TIM20_TRG_COM_IRQHandler
   Definitions
      At line 430 in file startup_stm32g474xx.s
   Uses
      At line 161 in file startup_stm32g474xx.s
      At line 328 in file startup_stm32g474xx.s

TIM20_UP_IRQHandler 0000001A

Symbol: TIM20_UP_IRQHandler
   Definitions
      At line 429 in file startup_stm32g474xx.s
   Uses
      At line 160 in file startup_stm32g474xx.s
      At line 327 in file startup_stm32g474xx.s




ARM Macro Assembler    Page 15 Alphabetic symbol ordering
Relocatable symbols

TIM2_IRQHandler 0000001A

Symbol: TIM2_IRQHandler
   Definitions
      At line 379 in file startup_stm32g474xx.s
   Uses
      At line 110 in file startup_stm32g474xx.s
      At line 277 in file startup_stm32g474xx.s

TIM3_IRQHandler 0000001A

Symbol: TIM3_IRQHandler
   Definitions
      At line 380 in file startup_stm32g474xx.s
   Uses
      At line 111 in file startup_stm32g474xx.s
      At line 278 in file startup_stm32g474xx.s

TIM4_IRQHandler 0000001A

Symbol: TIM4_IRQHandler
   Definitions
      At line 381 in file startup_stm32g474xx.s
   Uses
      At line 112 in file startup_stm32g474xx.s
      At line 279 in file startup_stm32g474xx.s

TIM5_IRQHandler 0000001A

Symbol: TIM5_IRQHandler
   Definitions
      At line 401 in file startup_stm32g474xx.s
   Uses
      At line 132 in file startup_stm32g474xx.s
      At line 299 in file startup_stm32g474xx.s

TIM6_DAC_IRQHandler 0000001A

Symbol: TIM6_DAC_IRQHandler
   Definitions
      At line 405 in file startup_stm32g474xx.s
   Uses
      At line 136 in file startup_stm32g474xx.s
      At line 303 in file startup_stm32g474xx.s

TIM7_DAC_IRQHandler 0000001A

Symbol: TIM7_DAC_IRQHandler
   Definitions
      At line 406 in file startup_stm32g474xx.s
   Uses
      At line 137 in file startup_stm32g474xx.s
      At line 304 in file startup_stm32g474xx.s

TIM8_BRK_IRQHandler 0000001A

Symbol: TIM8_BRK_IRQHandler
   Definitions
      At line 394 in file startup_stm32g474xx.s



ARM Macro Assembler    Page 16 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 125 in file startup_stm32g474xx.s
      At line 292 in file startup_stm32g474xx.s

TIM8_CC_IRQHandler 0000001A

Symbol: TIM8_CC_IRQHandler
   Definitions
      At line 397 in file startup_stm32g474xx.s
   Uses
      At line 128 in file startup_stm32g474xx.s
      At line 295 in file startup_stm32g474xx.s

TIM8_TRG_COM_IRQHandler 0000001A

Symbol: TIM8_TRG_COM_IRQHandler
   Definitions
      At line 396 in file startup_stm32g474xx.s
   Uses
      At line 127 in file startup_stm32g474xx.s
      At line 294 in file startup_stm32g474xx.s

TIM8_UP_IRQHandler 0000001A

Symbol: TIM8_UP_IRQHandler
   Definitions
      At line 395 in file startup_stm32g474xx.s
   Uses
      At line 126 in file startup_stm32g474xx.s
      At line 293 in file startup_stm32g474xx.s

UART4_IRQHandler 0000001A

Symbol: UART4_IRQHandler
   Definitions
      At line 403 in file startup_stm32g474xx.s
   Uses
      At line 134 in file startup_stm32g474xx.s
      At line 301 in file startup_stm32g474xx.s

UART5_IRQHandler 0000001A

Symbol: UART5_IRQHandler
   Definitions
      At line 404 in file startup_stm32g474xx.s
   Uses
      At line 135 in file startup_stm32g474xx.s
      At line 302 in file startup_stm32g474xx.s

UCPD1_IRQHandler 0000001A

Symbol: UCPD1_IRQHandler
   Definitions
      At line 414 in file startup_stm32g474xx.s
   Uses
      At line 145 in file startup_stm32g474xx.s
      At line 312 in file startup_stm32g474xx.s

USART1_IRQHandler 0000001A



ARM Macro Assembler    Page 17 Alphabetic symbol ordering
Relocatable symbols


Symbol: USART1_IRQHandler
   Definitions
      At line 388 in file startup_stm32g474xx.s
   Uses
      At line 119 in file startup_stm32g474xx.s
      At line 286 in file startup_stm32g474xx.s

USART2_IRQHandler 0000001A

Symbol: USART2_IRQHandler
   Definitions
      At line 389 in file startup_stm32g474xx.s
   Uses
      At line 120 in file startup_stm32g474xx.s
      At line 287 in file startup_stm32g474xx.s

USART3_IRQHandler 0000001A

Symbol: USART3_IRQHandler
   Definitions
      At line 390 in file startup_stm32g474xx.s
   Uses
      At line 121 in file startup_stm32g474xx.s
      At line 288 in file startup_stm32g474xx.s

USBWakeUp_IRQHandler 0000001A

Symbol: USBWakeUp_IRQHandler
   Definitions
      At line 393 in file startup_stm32g474xx.s
   Uses
      At line 124 in file startup_stm32g474xx.s
      At line 291 in file startup_stm32g474xx.s

USB_HP_IRQHandler 0000001A

Symbol: USB_HP_IRQHandler
   Definitions
      At line 370 in file startup_stm32g474xx.s
   Uses
      At line 101 in file startup_stm32g474xx.s
      At line 268 in file startup_stm32g474xx.s

USB_LP_IRQHandler 0000001A

Symbol: USB_LP_IRQHandler
   Definitions
      At line 371 in file startup_stm32g474xx.s
   Uses
      At line 102 in file startup_stm32g474xx.s
      At line 269 in file startup_stm32g474xx.s

UsageFault_Handler 00000010

Symbol: UsageFault_Handler
   Definitions
      At line 225 in file startup_stm32g474xx.s
   Uses



ARM Macro Assembler    Page 18 Alphabetic symbol ordering
Relocatable symbols

      At line 70 in file startup_stm32g474xx.s
      At line 226 in file startup_stm32g474xx.s

WWDG_IRQHandler 0000001A

Symbol: WWDG_IRQHandler
   Definitions
      At line 351 in file startup_stm32g474xx.s
   Uses
      At line 82 in file startup_stm32g474xx.s
      At line 249 in file startup_stm32g474xx.s

__user_initial_stackheap 0000001C

Symbol: __user_initial_stackheap
   Definitions
      At line 473 in file startup_stm32g474xx.s
   Uses
      At line 471 in file startup_stm32g474xx.s
Comment: __user_initial_stackheap used once
114 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000200

Symbol: Heap_Size
   Definitions
      At line 47 in file startup_stm32g474xx.s
   Uses
      At line 51 in file startup_stm32g474xx.s
      At line 477 in file startup_stm32g474xx.s

Stack_Size 00000400

Symbol: Stack_Size
   Definitions
      At line 36 in file startup_stm32g474xx.s
   Uses
      At line 39 in file startup_stm32g474xx.s
      At line 476 in file startup_stm32g474xx.s

__Vectors_Size 000001D8

Symbol: __Vectors_Size
   Definitions
      At line 187 in file startup_stm32g474xx.s
   Uses
      At line 62 in file startup_stm32g474xx.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 194 in file startup_stm32g474xx.s
   Uses
      At line 197 in file startup_stm32g474xx.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 195 in file startup_stm32g474xx.s
   Uses
      At line 199 in file startup_stm32g474xx.s
Comment: __main used once
__use_two_region_memory 00000000

Symbol: __use_two_region_memory
   Definitions
      At line 470 in file startup_stm32g474xx.s
   Uses
      None
Comment: __use_two_region_memory unused
3 symbols
468 symbols in table
