#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
STM32G4通信模块测试脚本
用于测试上位机与STM32G4之间的通信功能
"""

import serial
import struct
import time
import sys

class STM32CommTester:
    def __init__(self, port='COM3', baudrate=115200):
        """
        初始化通信测试器
        
        Args:
            port: 串口号
            baudrate: 波特率
        """
        self.port = port
        self.baudrate = baudrate
        self.ser = None
        
        # 命令类型定义
        self.CMD_TYPE_LED_CONTROL = 0x01
        self.CMD_TYPE_SYSTEM_INFO = 0x02
        self.CMD_TYPE_RESET = 0x03
        self.CMD_TYPE_STATUS = 0x04
        self.CMD_TYPE_TEST = 0x06
        
        # LED命令ID
        self.LED_CMD_ON = 0x01
        self.LED_CMD_OFF = 0x02
        self.LED_CMD_TOGGLE = 0x03
        self.LED_CMD_BLINK = 0x04
        
        # 系统命令ID
        self.SYS_CMD_GET_INFO = 0x01
        self.SYS_CMD_GET_VERSION = 0x02
        self.SYS_CMD_GET_STATUS = 0x03
        
        # 协议常量
        self.COMM_HEADER = 0xAA
        self.COMM_TAIL = 0x55
        self.RESP_HEADER = 0xBB
        self.RESP_TAIL = 0xAA
        
    def connect(self):
        """连接串口"""
        try:
            self.ser = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=2
            )
            print(f"成功连接到 {self.port}, 波特率: {self.baudrate}")
            return True
        except Exception as e:
            print(f"连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开串口连接"""
        if self.ser and self.ser.is_open:
            self.ser.close()
            print("串口已断开")
    
    def calculate_checksum(self, data):
        """计算校验和"""
        checksum = 0
        for byte in data:
            checksum ^= byte
        return checksum
    
    def create_command(self, cmd_type, cmd_id, data=None):
        """
        创建命令包
        
        Args:
            cmd_type: 命令类型
            cmd_id: 命令ID
            data: 数据（可选）
            
        Returns:
            bytes: 命令包
        """
        if data is None:
            data = []
        
        # 确保数据长度不超过32字节
        if len(data) > 32:
            data = data[:32]
        
        # 补齐数据到32字节
        data_padded = data + [0] * (32 - len(data))
        
        # 构建命令包（不包含校验和）
        cmd_bytes = [
            self.COMM_HEADER,
            len(data),
            cmd_type,
            cmd_id
        ] + data_padded
        
        # 计算校验和（从length开始到data结束）
        checksum = self.calculate_checksum(cmd_bytes[1:])
        
        # 添加校验和和尾部
        cmd_bytes.extend([checksum, self.COMM_TAIL])
        
        return bytes(cmd_bytes)
    
    def send_command(self, cmd_type, cmd_id, data=None):
        """
        发送命令并接收响应
        
        Args:
            cmd_type: 命令类型
            cmd_id: 命令ID
            data: 数据（可选）
            
        Returns:
            dict: 响应数据
        """
        if not self.ser or not self.ser.is_open:
            print("串口未连接")
            return None
        
        # 创建命令
        command = self.create_command(cmd_type, cmd_id, data)
        
        print(f"发送命令: {' '.join([f'{b:02X}' for b in command])}")
        
        # 发送命令
        self.ser.write(command)
        
        # 接收响应
        response = self.receive_response()
        return response
    
    def receive_response(self):
        """接收响应"""
        try:
            # 响应包固定长度：1+1+1+1+1+32+1+1 = 38字节
            response_data = self.ser.read(38)
            
            if len(response_data) != 38:
                print(f"响应长度错误: {len(response_data)}")
                return None
            
            print(f"接收响应: {' '.join([f'{b:02X}' for b in response_data])}")
            
            # 解析响应
            header = response_data[0]
            length = response_data[1]
            cmd_type = response_data[2]
            cmd_id = response_data[3]
            status = response_data[4]
            data = list(response_data[5:37])
            checksum = response_data[37]
            tail = response_data[38] if len(response_data) > 38 else 0
            
            # 验证响应格式
            if header != self.RESP_HEADER:
                print(f"响应头错误: {header:02X}")
                return None
            
            # 验证校验和
            calc_checksum = self.calculate_checksum(response_data[1:37])
            if calc_checksum != checksum:
                print(f"校验和错误: 计算值={calc_checksum:02X}, 接收值={checksum:02X}")
                return None
            
            return {
                'cmd_type': cmd_type,
                'cmd_id': cmd_id,
                'status': status,
                'data': data[:length] if length <= 32 else data,
                'length': length
            }
            
        except Exception as e:
            print(f"接收响应失败: {e}")
            return None
    
    def test_led_control(self):
        """测试LED控制命令"""
        print("\n=== LED控制测试 ===")
        
        # 测试LED开
        print("1. 测试LED开启")
        response = self.send_command(self.CMD_TYPE_LED_CONTROL, self.LED_CMD_ON)
        if response:
            print(f"响应状态: {response['status']}, 数据: {response['data']}")
        
        time.sleep(1)
        
        # 测试LED关
        print("2. 测试LED关闭")
        response = self.send_command(self.CMD_TYPE_LED_CONTROL, self.LED_CMD_OFF)
        if response:
            print(f"响应状态: {response['status']}, 数据: {response['data']}")
        
        time.sleep(1)
        
        # 测试LED切换
        print("3. 测试LED切换")
        response = self.send_command(self.CMD_TYPE_LED_CONTROL, self.LED_CMD_TOGGLE)
        if response:
            print(f"响应状态: {response['status']}, 数据: {response['data']}")
        
        time.sleep(1)
        
        # 测试LED闪烁
        print("4. 测试LED闪烁（3次，间隔500ms）")
        blink_data = [3, 0x01, 0xF4]  # 3次，500ms间隔
        response = self.send_command(self.CMD_TYPE_LED_CONTROL, self.LED_CMD_BLINK, blink_data)
        if response:
            print(f"响应状态: {response['status']}, 数据: {response['data']}")
    
    def test_system_info(self):
        """测试系统信息查询"""
        print("\n=== 系统信息测试 ===")
        
        # 获取系统信息
        print("1. 获取系统信息")
        response = self.send_command(self.CMD_TYPE_SYSTEM_INFO, self.SYS_CMD_GET_INFO)
        if response:
            info = ''.join([chr(b) for b in response['data'] if b != 0])
            print(f"系统信息: {info}")
        
        # 获取版本信息
        print("2. 获取版本信息")
        response = self.send_command(self.CMD_TYPE_SYSTEM_INFO, self.SYS_CMD_GET_VERSION)
        if response:
            version = ''.join([chr(b) for b in response['data'] if b != 0])
            print(f"版本信息: {version}")
        
        # 获取状态信息
        print("3. 获取状态信息")
        response = self.send_command(self.CMD_TYPE_STATUS, self.SYS_CMD_GET_STATUS)
        if response:
            if len(response['data']) >= 5:
                state = response['data'][0]
                total_received = (response['data'][1] << 8) | response['data'][2]
                total_errors = (response['data'][3] << 8) | response['data'][4]
                print(f"通信状态: {state}, 总接收: {total_received}, 总错误: {total_errors}")
    
    def test_echo(self):
        """测试回显功能"""
        print("\n=== 回显测试 ===")
        
        test_data = [0x12, 0x34, 0x56, 0x78, 0xAB, 0xCD, 0xEF]
        print(f"发送测试数据: {' '.join([f'{b:02X}' for b in test_data])}")
        
        response = self.send_command(self.CMD_TYPE_TEST, 0x01, test_data)
        if response:
            received_data = response['data'][:len(test_data)]
            print(f"接收数据: {' '.join([f'{b:02X}' for b in received_data])}")
            
            if received_data == test_data:
                print("回显测试通过！")
            else:
                print("回显测试失败！")
    
    def run_all_tests(self):
        """运行所有测试"""
        if not self.connect():
            return
        
        try:
            self.test_led_control()
            self.test_system_info()
            self.test_echo()
            
            print("\n=== 测试完成 ===")
            
        except KeyboardInterrupt:
            print("\n测试被用户中断")
        except Exception as e:
            print(f"\n测试过程中发生错误: {e}")
        finally:
            self.disconnect()

def main():
    """主函数"""
    if len(sys.argv) > 1:
        port = sys.argv[1]
    else:
        port = 'COM3'  # 默认串口
    
    print(f"STM32G4通信模块测试")
    print(f"使用串口: {port}")
    print("请确保STM32设备已连接并正常运行")
    
    tester = STM32CommTester(port)
    tester.run_all_tests()

if __name__ == "__main__":
    main()
