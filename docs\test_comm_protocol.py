#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
STM32G4 串口通信协议测试脚本

作者: mkx
日期: 2025-08-04
版本: 1.0.0

功能:
- 测试串口通信协议的各种命令
- 验证数据包格式和校验机制
- 提供自动化测试功能

使用方法:
python test_comm_protocol.py [COM端口] [波特率]

示例:
python test_comm_protocol.py COM3 115200
"""

import serial
import time
import sys
import struct
from typing import List, Tuple, Optional

class CommProtocolTester:
    """串口通信协议测试类"""
    
    # 协议常量
    FRAME_HEADER = 0xAA55
    FRAME_TAIL = 0x55AA
    
    # 命令类型
    CMD_PING = 0x01
    CMD_ACK = 0x02
    CMD_NACK = 0x03
    CMD_GET_VERSION = 0x10
    CMD_GET_STATUS = 0x11
    CMD_SET_CONFIG = 0x20
    CMD_GET_CONFIG = 0x21
    CMD_RESET = 0x30
    CMD_LED_CONTROL = 0x40
    
    # 错误码
    ERROR_CODES = {
        0x00: "COMM_OK",
        0x01: "COMM_ERROR",
        0x02: "COMM_ERROR_PARAM",
        0x03: "COMM_ERROR_TIMEOUT",
        0x04: "COMM_ERROR_CHECKSUM",
        0x05: "COMM_ERROR_FRAME",
        0x06: "COMM_ERROR_BUFFER_FULL",
        0x07: "COMM_ERROR_NOT_INIT",
        0x08: "COMM_ERROR_BUSY",
        0x09: "COMM_ERROR_NO_MEMORY",
        0x0A: "COMM_ERROR_UNSUPPORTED"
    }
    
    def __init__(self, port: str, baudrate: int = 115200):
        """
        初始化测试器
        
        Args:
            port: 串口端口名
            baudrate: 波特率
        """
        self.port = port
        self.baudrate = baudrate
        self.serial = None
        
    def connect(self) -> bool:
        """
        连接串口
        
        Returns:
            bool: 连接成功返回True，失败返回False
        """
        try:
            self.serial = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=8,
                parity='N',
                stopbits=1,
                timeout=2.0
            )
            print(f"成功连接到 {self.port}，波特率 {self.baudrate}")
            return True
        except Exception as e:
            print(f"连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开串口连接"""
        if self.serial and self.serial.is_open:
            self.serial.close()
            print("串口已断开")
    
    def calculate_checksum(self, data: bytes) -> int:
        """
        计算校验码
        
        Args:
            data: 需要校验的数据
            
        Returns:
            int: 校验码
        """
        checksum = 0
        for byte in data:
            checksum += byte
        return checksum & 0xFF
    
    def build_frame(self, cmd_type: int, payload: bytes = b'') -> bytes:
        """
        构建数据帧
        
        Args:
            cmd_type: 命令类型
            payload: 有效载荷
            
        Returns:
            bytes: 完整的数据帧
        """
        frame = bytearray()
        
        # 帧头
        frame.extend(struct.pack('>H', self.FRAME_HEADER))
        
        # 命令类型
        frame.append(cmd_type)
        
        # 数据长度
        frame.append(len(payload))
        
        # 有效载荷
        frame.extend(payload)
        
        # 校验码
        checksum_data = bytes([cmd_type, len(payload)]) + payload
        checksum = self.calculate_checksum(checksum_data)
        frame.append(checksum)
        
        # 帧尾
        frame.extend(struct.pack('>H', self.FRAME_TAIL))
        
        return bytes(frame)
    
    def parse_frame(self, data: bytes) -> Optional[Tuple[int, bytes]]:
        """
        解析数据帧
        
        Args:
            data: 接收到的数据
            
        Returns:
            Tuple[int, bytes]: (命令类型, 有效载荷) 或 None
        """
        if len(data) < 7:  # 最小帧长度
            return None
        
        # 检查帧头
        header = struct.unpack('>H', data[0:2])[0]
        if header != self.FRAME_HEADER:
            return None
        
        # 提取字段
        cmd_type = data[2]
        data_len = data[3]
        
        if len(data) < 7 + data_len:
            return None
        
        payload = data[4:4+data_len]
        checksum = data[4+data_len]
        
        # 检查帧尾
        tail_offset = 5 + data_len
        if len(data) < tail_offset + 2:
            return None
        
        tail = struct.unpack('>H', data[tail_offset:tail_offset+2])[0]
        if tail != self.FRAME_TAIL:
            return None
        
        # 验证校验码
        checksum_data = bytes([cmd_type, data_len]) + payload
        calculated_checksum = self.calculate_checksum(checksum_data)
        if checksum != calculated_checksum:
            print(f"校验错误: 期望 {calculated_checksum:02X}, 收到 {checksum:02X}")
            return None
        
        return cmd_type, payload
    
    def send_command(self, cmd_type: int, payload: bytes = b'', timeout: float = 2.0) -> Optional[Tuple[int, bytes]]:
        """
        发送命令并等待响应
        
        Args:
            cmd_type: 命令类型
            payload: 有效载荷
            timeout: 超时时间
            
        Returns:
            Tuple[int, bytes]: (响应命令类型, 响应数据) 或 None
        """
        if not self.serial or not self.serial.is_open:
            print("串口未连接")
            return None
        
        # 构建并发送帧
        frame = self.build_frame(cmd_type, payload)
        print(f"发送: {frame.hex().upper()}")
        
        self.serial.write(frame)
        
        # 等待响应
        start_time = time.time()
        buffer = bytearray()
        
        while time.time() - start_time < timeout:
            if self.serial.in_waiting > 0:
                data = self.serial.read(self.serial.in_waiting)
                buffer.extend(data)
                
                # 尝试解析帧
                result = self.parse_frame(bytes(buffer))
                if result:
                    print(f"接收: {buffer.hex().upper()}")
                    return result
            
            time.sleep(0.01)
        
        print("响应超时")
        return None
    
    def test_ping(self) -> bool:
        """测试心跳命令"""
        print("\n=== 测试心跳命令 ===")
        result = self.send_command(self.CMD_PING)
        
        if result:
            cmd_type, payload = result
            if cmd_type == self.CMD_ACK and len(payload) == 1 and payload[0] == self.CMD_PING:
                print("✓ 心跳测试通过")
                return True
            else:
                print("✗ 心跳测试失败: 响应格式错误")
        else:
            print("✗ 心跳测试失败: 无响应")
        
        return False
    
    def test_get_version(self) -> bool:
        """测试获取版本信息"""
        print("\n=== 测试获取版本信息 ===")
        result = self.send_command(self.CMD_GET_VERSION)
        
        if result:
            cmd_type, payload = result
            if cmd_type == self.CMD_GET_VERSION and len(payload) == 3:
                major, minor, patch = payload
                print(f"✓ 版本信息: {major}.{minor}.{patch}")
                return True
            else:
                print("✗ 版本测试失败: 响应格式错误")
        else:
            print("✗ 版本测试失败: 无响应")
        
        return False
    
    def test_get_status(self) -> bool:
        """测试获取状态信息"""
        print("\n=== 测试获取状态信息 ===")
        result = self.send_command(self.CMD_GET_STATUS)
        
        if result:
            cmd_type, payload = result
            if cmd_type == self.CMD_GET_STATUS and len(payload) == 4:
                state, initialized, receiving, transmitting = payload
                print(f"✓ 状态信息:")
                print(f"  - 模块状态: {state}")
                print(f"  - 已初始化: {'是' if initialized else '否'}")
                print(f"  - 正在接收: {'是' if receiving else '否'}")
                print(f"  - 正在发送: {'是' if transmitting else '否'}")
                return True
            else:
                print("✗ 状态测试失败: 响应格式错误")
        else:
            print("✗ 状态测试失败: 无响应")
        
        return False
    
    def test_led_control(self) -> bool:
        """测试LED控制"""
        print("\n=== 测试LED控制 ===")
        
        # 测试开启LED1
        payload = bytes([1, 1])  # LED1, 开启
        result = self.send_command(self.CMD_LED_CONTROL, payload)
        
        if result:
            cmd_type, response_payload = result
            if cmd_type == self.CMD_ACK:
                print("✓ LED1开启成功")
            elif cmd_type == self.CMD_LED_CONTROL and len(response_payload) == 2:
                led_id, state = response_payload
                print(f"✓ LED{led_id} 状态: {'开启' if state else '关闭'}")
            else:
                print("✗ LED控制测试失败: 响应格式错误")
                return False
        else:
            print("✗ LED控制测试失败: 无响应")
            return False
        
        time.sleep(1)
        
        # 测试关闭LED1
        payload = bytes([1, 0])  # LED1, 关闭
        result = self.send_command(self.CMD_LED_CONTROL, payload)
        
        if result:
            print("✓ LED1关闭成功")
            return True
        else:
            print("✗ LED关闭测试失败")
            return False
    
    def test_get_config(self) -> bool:
        """测试获取配置"""
        print("\n=== 测试获取配置 ===")
        
        # 获取系统时钟频率
        payload = bytes([0x01])
        result = self.send_command(self.CMD_GET_CONFIG, payload)
        
        if result:
            cmd_type, response_payload = result
            if cmd_type == self.CMD_GET_CONFIG and len(response_payload) == 4:
                sysclk = struct.unpack('>I', response_payload)[0]
                print(f"✓ 系统时钟频率: {sysclk} Hz")
                return True
            else:
                print("✗ 配置测试失败: 响应格式错误")
        else:
            print("✗ 配置测试失败: 无响应")
        
        return False
    
    def run_all_tests(self) -> bool:
        """运行所有测试"""
        print("开始串口通信协议测试...")
        
        if not self.connect():
            return False
        
        try:
            tests = [
                self.test_ping,
                self.test_get_version,
                self.test_get_status,
                self.test_led_control,
                self.test_get_config
            ]
            
            passed = 0
            total = len(tests)
            
            for test in tests:
                if test():
                    passed += 1
                time.sleep(0.5)  # 测试间隔
            
            print(f"\n=== 测试结果 ===")
            print(f"通过: {passed}/{total}")
            print(f"成功率: {passed/total*100:.1f}%")
            
            return passed == total
            
        finally:
            self.disconnect()

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python test_comm_protocol.py <COM端口> [波特率]")
        print("示例: python test_comm_protocol.py COM3 115200")
        return
    
    port = sys.argv[1]
    baudrate = int(sys.argv[2]) if len(sys.argv) > 2 else 115200
    
    tester = CommProtocolTester(port, baudrate)
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
