/**
 ******************************************************************************
 * @file    usart.h
 * @brief   This file contains all the function prototypes for
 *          the usart.c file
 ******************************************************************************
 * @attention
 *
 * <h2><center>&copy; Copyright (c) 2025 STMicroelectronics.
 * All rights reserved.</center></h2>
 *
 * This software component is licensed by ST under Ultimate Liberty license
 * SLA0044, the "License"; You may not use this file except in compliance with
 * the License. You may obtain a copy of the License at:
 *                             www.st.com/SLA0044
 *
 ******************************************************************************
 */
/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __USART_H__
#define __USART_H__

#ifdef __cplusplus
extern "C"
{
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"

    /* USER CODE BEGIN Includes */
#include <stdio.h>
    /* USER CODE END Includes */

    extern UART_HandleTypeDef huart1;
    extern UART_HandleTypeDef huart2;

    /* USER CODE BEGIN Private defines */

    /* USER CODE END Private defines */

    void MX_USART1_UART_Init(void);
    void MX_USART2_UART_Init(void);

    /* USER CODE BEGIN Prototypes */

    /* printf重定向相关函数 */
    int uart2_puts(const char *str);
    int uart2_write(const uint8_t *data, uint16_t len);

    /* USER CODE END Prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __USART_H__ */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
