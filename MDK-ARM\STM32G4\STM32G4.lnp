--cpu=Cortex-M4.fp.sp
"stm32g4\startup_stm32g474xx.o"
"stm32g4\main.o"
"stm32g4\gpio.o"
"stm32g4\app_freertos.o"
"stm32g4\usart.o"
"stm32g4\stm32g4xx_it.o"
"stm32g4\stm32g4xx_hal_msp.o"
"stm32g4\stm32g4xx_hal.o"
"stm32g4\stm32g4xx_hal_rcc.o"
"stm32g4\stm32g4xx_hal_rcc_ex.o"
"stm32g4\stm32g4xx_hal_flash.o"
"stm32g4\stm32g4xx_hal_flash_ex.o"
"stm32g4\stm32g4xx_hal_flash_ramfunc.o"
"stm32g4\stm32g4xx_hal_gpio.o"
"stm32g4\stm32g4xx_hal_exti.o"
"stm32g4\stm32g4xx_hal_dma.o"
"stm32g4\stm32g4xx_hal_dma_ex.o"
"stm32g4\stm32g4xx_hal_pwr.o"
"stm32g4\stm32g4xx_hal_pwr_ex.o"
"stm32g4\stm32g4xx_hal_cortex.o"
"stm32g4\stm32g4xx_hal_tim.o"
"stm32g4\stm32g4xx_hal_tim_ex.o"
"stm32g4\stm32g4xx_hal_uart.o"
"stm32g4\stm32g4xx_hal_uart_ex.o"
"stm32g4\system_stm32g4xx.o"
"stm32g4\croutine.o"
"stm32g4\event_groups.o"
"stm32g4\list.o"
"stm32g4\queue.o"
"stm32g4\stream_buffer.o"
"stm32g4\tasks.o"
"stm32g4\timers.o"
"stm32g4\cmsis_os2.o"
"stm32g4\heap_4.o"
"stm32g4\port.o"
"stm32g4\led.o"
"stm32g4\comm_module.o"
--strict --scatter "STM32G4\STM32G4.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "STM32G4.map" -o STM32G4\STM32G4.axf